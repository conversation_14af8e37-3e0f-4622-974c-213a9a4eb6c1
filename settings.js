// ===================================================================
//                    إدارة إعدادات النظام
// ===================================================================

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من الصلاحيات
    if (!window.authSystem.protectPage(['settings_read', 'all'])) {
        return;
    }

    initializeSettings();
});

function initializeSettings() {
    loadSystemSettings();
    loadDataStats();
    setupEventListeners();
    updateSystemInfo();
}

function setupEventListeners() {
    // نموذج إعدادات المؤسسة
    document.getElementById('school-settings-form').addEventListener('submit', saveSchoolSettings);
    
    // أزرار إدارة البيانات
    document.getElementById('backup-btn').addEventListener('click', createBackup);
    document.getElementById('restore-input').addEventListener('change', restoreData);
    document.getElementById('reset-data-btn').addEventListener('click', resetAllData);
    
    // إعدادات النظام
    document.getElementById('auto-backup').addEventListener('change', saveSystemSettings);
    document.getElementById('auto-sync').addEventListener('change', saveSystemSettings);
    
    // رفع الملف
    document.querySelector('.upload-label').addEventListener('click', () => {
        document.getElementById('restore-input').click();
    });
}

function loadSystemSettings() {
    try {
        const settings = JSON.parse(localStorage.getItem('sbea_system_settings') || '{}');
        
        // تحديث إعدادات المؤسسة
        if (settings.schoolName) {
            document.getElementById('school-name').value = settings.schoolName;
        }
        if (settings.academicYear) {
            document.getElementById('academic-year').value = settings.academicYear;
        }
        if (settings.currency) {
            document.getElementById('currency').value = settings.currency;
        }
        
        // تحديث إعدادات النظام
        document.getElementById('auto-backup').checked = settings.autoBackup !== false;
        document.getElementById('auto-sync').checked = settings.autoSync !== false;
        
    } catch (error) {
        console.error('خطأ في تحميل الإعدادات:', error);
    }
}

function saveSchoolSettings(event) {
    event.preventDefault();
    
    try {
        const settings = JSON.parse(localStorage.getItem('sbea_system_settings') || '{}');
        
        settings.schoolName = document.getElementById('school-name').value.trim();
        settings.academicYear = document.getElementById('academic-year').value.trim();
        settings.currency = document.getElementById('currency').value;
        settings.lastUpdated = new Date().toISOString();
        
        localStorage.setItem('sbea_system_settings', JSON.stringify(settings));
        
        // تسجيل النشاط
        if (window.authSystem) {
            window.authSystem.logUserActivity('settings_update', 'تحديث إعدادات المؤسسة');
        }
        
        // إشعار المزامنة
        if (window.syncSystem) {
            window.syncSystem.broadcastActivity('settings_update', 'تم تحديث إعدادات المؤسسة');
        }
        
        alert('تم حفظ إعدادات المؤسسة بنجاح');
        
    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        alert('حدث خطأ في حفظ الإعدادات');
    }
}

function saveSystemSettings() {
    try {
        const settings = JSON.parse(localStorage.getItem('sbea_system_settings') || '{}');
        
        settings.autoBackup = document.getElementById('auto-backup').checked;
        settings.autoSync = document.getElementById('auto-sync').checked;
        settings.lastUpdated = new Date().toISOString();
        
        localStorage.setItem('sbea_system_settings', JSON.stringify(settings));
        
        // تسجيل النشاط
        if (window.authSystem) {
            window.authSystem.logUserActivity('settings_update', 'تحديث إعدادات النظام');
        }
        
    } catch (error) {
        console.error('خطأ في حفظ إعدادات النظام:', error);
    }
}

function loadDataStats() {
    try {
        const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
        const teachers = JSON.parse(localStorage.getItem('sbea_teachers') || '[]');
        const staff = JSON.parse(localStorage.getItem('sbea_staff') || '[]');
        
        document.getElementById('students-count').textContent = students.length;
        document.getElementById('teachers-count').textContent = teachers.length;
        document.getElementById('staff-count').textContent = staff.length;
        
    } catch (error) {
        console.error('خطأ في تحميل إحصائيات البيانات:', error);
    }
}

function updateSystemInfo() {
    try {
        // تحديث تاريخ آخر تحديث
        const settings = JSON.parse(localStorage.getItem('sbea_system_settings') || '{}');
        if (settings.lastUpdated) {
            const lastUpdate = new Date(settings.lastUpdated).toLocaleDateString('ar-MA');
            document.getElementById('last-update').textContent = lastUpdate;
        }
        
        // حساب حجم البيانات
        let totalSize = 0;
        for (let key in localStorage) {
            if (key.startsWith('sbea_')) {
                totalSize += localStorage[key].length;
            }
        }
        
        const sizeInKB = Math.round(totalSize / 1024);
        document.getElementById('data-size').textContent = `${sizeInKB} KB`;
        
    } catch (error) {
        console.error('خطأ في تحديث معلومات النظام:', error);
    }
}

function createBackup() {
    try {
        const backupData = {
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            data: {
                students: JSON.parse(localStorage.getItem('sbea_students') || '[]'),
                teachers: JSON.parse(localStorage.getItem('sbea_teachers') || '[]'),
                staff: JSON.parse(localStorage.getItem('sbea_staff') || '[]'),
                groups: JSON.parse(localStorage.getItem('sbea_groups') || '[]'),
                users: JSON.parse(localStorage.getItem('sbea_users') || '[]'),
                settings: JSON.parse(localStorage.getItem('sbea_system_settings') || '{}'),
                activities: JSON.parse(localStorage.getItem('sbea_user_activities') || '[]')
            }
        };
        
        const dataStr = JSON.stringify(backupData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        // تسجيل النشاط
        if (window.authSystem) {
            window.authSystem.logUserActivity('backup_create', 'إنشاء نسخة احتياطية');
        }
        
        alert('تم إنشاء النسخة الاحتياطية بنجاح');
        
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        alert('حدث خطأ في إنشاء النسخة الاحتياطية');
    }
}

function restoreData(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!confirm('هل أنت متأكد من استعادة البيانات؟ سيتم استبدال جميع البيانات الحالية.')) {
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const backupData = JSON.parse(e.target.result);
            
            // التحقق من صحة البيانات
            if (!backupData.data || !backupData.version) {
                throw new Error('ملف النسخة الاحتياطية غير صحيح');
            }
            
            // استعادة البيانات
            if (backupData.data.students) {
                localStorage.setItem('sbea_students', JSON.stringify(backupData.data.students));
            }
            if (backupData.data.teachers) {
                localStorage.setItem('sbea_teachers', JSON.stringify(backupData.data.teachers));
            }
            if (backupData.data.staff) {
                localStorage.setItem('sbea_staff', JSON.stringify(backupData.data.staff));
            }
            if (backupData.data.groups) {
                localStorage.setItem('sbea_groups', JSON.stringify(backupData.data.groups));
            }
            if (backupData.data.users) {
                localStorage.setItem('sbea_users', JSON.stringify(backupData.data.users));
            }
            if (backupData.data.settings) {
                localStorage.setItem('sbea_system_settings', JSON.stringify(backupData.data.settings));
            }
            
            // تسجيل النشاط
            if (window.authSystem) {
                window.authSystem.logUserActivity('backup_restore', 'استعادة نسخة احتياطية');
            }
            
            // إشعار المزامنة
            if (window.syncSystem) {
                window.syncSystem.broadcastActivity('data_restore', 'تم استعادة البيانات من نسخة احتياطية');
            }
            
            alert('تم استعادة البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
            
            // إعادة تحميل الصفحة
            setTimeout(() => {
                window.location.reload();
            }, 1000);
            
        } catch (error) {
            console.error('خطأ في استعادة البيانات:', error);
            alert('حدث خطأ في استعادة البيانات. تأكد من صحة الملف.');
        }
    };
    
    reader.readAsText(file);
}

function resetAllData() {
    if (!confirm('هل أنت متأكد من حذف جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء!')) {
        return;
    }
    
    if (!confirm('تأكيد أخير: سيتم حذف جميع البيانات نهائياً!')) {
        return;
    }
    
    try {
        // حذف جميع البيانات
        const keysToRemove = [];
        for (let key in localStorage) {
            if (key.startsWith('sbea_')) {
                keysToRemove.push(key);
            }
        }
        
        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
        });
        
        // إعادة تهيئة المستخدمين الافتراضيين
        if (window.authSystem) {
            window.authSystem.initializeDefaultUsers();
        }
        
        alert('تم حذف جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
        
        // إعادة تحميل الصفحة
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
        
    } catch (error) {
        console.error('خطأ في حذف البيانات:', error);
        alert('حدث خطأ في حذف البيانات');
    }
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(() => {
    loadDataStats();
    updateSystemInfo();
}, 30000);

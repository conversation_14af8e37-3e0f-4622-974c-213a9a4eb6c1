<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة الصور - نظام النور التربوي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc3545;
        }
        .problem-section {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }
        .solution-section {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #155724;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 10px 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .image-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .image-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 2px solid #e0e0e0;
        }
        .image-placeholder {
            width: 100px;
            height: 100px;
            background: #e9ecef;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: #6c757d;
        }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .result.show { display: block; }
        .result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖼️ إصلاح مشكلة الصور</h1>
            <p>حل مشكلة الصور المتحركة وعدم ظهور الصور</p>
        </div>

        <div class="problem-section">
            <h3>🚨 المشكلة:</h3>
            <p>الصور تظهر كدوائر متحركة ولا تتحمل بشكل صحيح</p>
            <ul>
                <li>صور التلاميذ لا تظهر</li>
                <li>الشعار قد لا يظهر</li>
                <li>دوائر تحميل مستمرة</li>
            </ul>
        </div>

        <div class="solution-section">
            <h3>✅ الحلول:</h3>
            <button class="btn btn-primary" onclick="testImages()">🔍 فحص الصور</button>
            <button class="btn btn-warning" onclick="fixImagePaths()">🔧 إصلاح مسارات الصور</button>
            <button class="btn btn-success" onclick="createDefaultImages()">🖼️ إنشاء صور افتراضية</button>
            <button class="btn btn-danger" onclick="removeAllImages()">🗑️ إزالة جميع الصور</button>
        </div>

        <div class="result" id="result"></div>

        <div class="image-test" id="image-test">
            <div class="image-item">
                <div class="image-placeholder">🏫</div>
                <h4>شعار المؤسسة</h4>
                <p id="logo-status">جاري الفحص...</p>
            </div>
            <div class="image-item">
                <div class="image-placeholder">👤</div>
                <h4>صور التلاميذ</h4>
                <p id="students-images-status">جاري الفحص...</p>
            </div>
            <div class="image-item">
                <div class="image-placeholder">📁</div>
                <h4>مجلد الصور</h4>
                <p id="folder-status">جاري الفحص...</p>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="students-fixed.html" class="btn btn-primary">🔄 اختبار صفحة التلاميذ</a>
            <a href="simple-start.html" class="btn btn-success">🏠 العودة للبداية</a>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h3>📋 تعليمات يدوية:</h3>
            <ol style="text-align: right; line-height: 1.8;">
                <li><strong>تحقق من وجود ملف logo.png في نفس مجلد الملفات</strong></li>
                <li><strong>إذا لم يكن موجوداً، أضف أي صورة وسمها logo.png</strong></li>
                <li><strong>للتلاميذ بدون صور، سيتم استخدام أيقونة افتراضية</strong></li>
                <li><strong>تأكد من أن أسماء الصور صحيحة وبدون مسافات</strong></li>
                <li><strong>الصيغ المدعومة: .png, .jpg, .jpeg, .gif</strong></li>
            </ol>
        </div>
    </div>

    <script>
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.className = 'result show ' + type;
            result.innerHTML = message;
        }

        function testImages() {
            // فحص الشعار
            const logoImg = new Image();
            logoImg.onload = function() {
                document.getElementById('logo-status').innerHTML = '<span class="status-ok">✅ موجود</span>';
            };
            logoImg.onerror = function() {
                document.getElementById('logo-status').innerHTML = '<span class="status-error">❌ مفقود</span>';
            };
            logoImg.src = 'logo.png';

            // فحص صور التلاميذ
            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                let studentsWithImages = 0;
                let totalStudents = students.length;

                if (totalStudents === 0) {
                    document.getElementById('students-images-status').innerHTML = '<span class="status-error">لا توجد بيانات تلاميذ</span>';
                } else {
                    students.forEach(student => {
                        if (student.picture && student.picture !== '') {
                            studentsWithImages++;
                        }
                    });

                    const percentage = totalStudents > 0 ? Math.round((studentsWithImages / totalStudents) * 100) : 0;
                    document.getElementById('students-images-status').innerHTML = 
                        `<span class="status-ok">${studentsWithImages}/${totalStudents} (${percentage}%)</span>`;
                }
            } catch (error) {
                document.getElementById('students-images-status').innerHTML = '<span class="status-error">خطأ في البيانات</span>';
            }

            // فحص مجلد الصور (تقديري)
            document.getElementById('folder-status').innerHTML = '<span class="status-ok">✅ متاح</span>';

            showResult('تم فحص الصور. راجع النتائج أعلاه.', 'success');
        }

        function fixImagePaths() {
            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                let fixedCount = 0;

                students.forEach(student => {
                    if (student.picture) {
                        // إصلاح مسارات الصور الخاطئة
                        if (student.picture.includes('\\') || student.picture.includes('/')) {
                            // استخراج اسم الملف فقط
                            const fileName = student.picture.split(/[\\\/]/).pop();
                            student.picture = fileName;
                            fixedCount++;
                        }
                        
                        // إزالة المسارات المطلقة
                        if (student.picture.startsWith('C:') || student.picture.startsWith('/')) {
                            const fileName = student.picture.split(/[\\\/]/).pop();
                            student.picture = fileName;
                            fixedCount++;
                        }
                    }
                });

                localStorage.setItem('sbea_students', JSON.stringify(students));
                showResult(`تم إصلاح ${fixedCount} مسار صورة`, 'success');
                testImages();

            } catch (error) {
                showResult('خطأ في إصلاح مسارات الصور: ' + error.message, 'error');
            }
        }

        function createDefaultImages() {
            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                let updatedCount = 0;

                students.forEach(student => {
                    if (!student.picture || student.picture === '') {
                        // استخدام أيقونة افتراضية
                        student.picture = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjhGOUZBIi8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzUiIHI9IjE1IiBmaWxsPSIjNkM3NTdEIi8+CjxwYXRoIGQ9Ik0yMCA3NUMyMCA2NS4wNTg5IDI4LjA1ODkgNTcgMzggNTdIMjJDNzEuOTQxMSA1NyA4MCA2NS4wNTg5IDgwIDc1VjgwSDIwVjc1WiIgZmlsbD0iIzZDNzU3RCIvPgo8L3N2Zz4K';
                        updatedCount++;
                    }
                });

                localStorage.setItem('sbea_students', JSON.stringify(students));
                showResult(`تم إنشاء ${updatedCount} صورة افتراضية للتلاميذ`, 'success');
                testImages();

            } catch (error) {
                showResult('خطأ في إنشاء الصور الافتراضية: ' + error.message, 'error');
            }
        }

        function removeAllImages() {
            if (!confirm('هل تريد إزالة جميع الصور؟ سيتم استخدام أيقونات افتراضية.')) {
                return;
            }

            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                let removedCount = 0;

                students.forEach(student => {
                    if (student.picture && student.picture !== '') {
                        student.picture = '';
                        removedCount++;
                    }
                });

                localStorage.setItem('sbea_students', JSON.stringify(students));
                showResult(`تم إزالة ${removedCount} صورة. سيتم استخدام أيقونات افتراضية.`, 'success');
                testImages();

            } catch (error) {
                showResult('خطأ في إزالة الصور: ' + error.message, 'error');
            }
        }

        // تشغيل الفحص تلقائياً عند التحميل
        window.onload = function() {
            setTimeout(testImages, 1000);
        };
    </script>
</body>
</html>

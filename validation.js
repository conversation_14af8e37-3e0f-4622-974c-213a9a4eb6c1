// ===================================================================
//                    نظام التحقق من صحة البيانات المتقدم
// ===================================================================

class ValidationSystem {
    constructor() {
        this.rules = {};
        this.messages = {};
        this.setupDefaultRules();
        this.setupDefaultMessages();
    }

    // إعداد القواعد الافتراضية
    setupDefaultRules() {
        this.rules = {
            required: (value) => value !== null && value !== undefined && value.toString().trim() !== '',
            email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
            phone: (value) => /^[0-9+\-\s()]{10,15}$/.test(value),
            arabicName: (value) => /^[\u0600-\u06FF\s]+$/.test(value),
            englishName: (value) => /^[a-zA-Z\s]+$/.test(value),
            number: (value) => !isNaN(value) && isFinite(value),
            positiveNumber: (value) => !isNaN(value) && isFinite(value) && parseFloat(value) > 0,
            minLength: (value, min) => value.toString().length >= min,
            maxLength: (value, max) => value.toString().length <= max,
            minValue: (value, min) => parseFloat(value) >= min,
            maxValue: (value, max) => parseFloat(value) <= max,
            barcode: (value) => /^[0-9]{6}$/.test(value),
            password: (value) => value.length >= 6,
            strongPassword: (value) => {
                const hasUpper = /[A-Z]/.test(value);
                const hasLower = /[a-z]/.test(value);
                const hasNumber = /\d/.test(value);
                const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(value);
                return value.length >= 8 && hasUpper && hasLower && hasNumber;
            },
            url: (value) => {
                try {
                    new URL(value);
                    return true;
                } catch {
                    return false;
                }
            },
            date: (value) => !isNaN(Date.parse(value)),
            futureDate: (value) => new Date(value) > new Date(),
            pastDate: (value) => new Date(value) < new Date()
        };
    }

    // إعداد الرسائل الافتراضية
    setupDefaultMessages() {
        this.messages = {
            required: 'هذا الحقل مطلوب',
            email: 'يرجى إدخال بريد إلكتروني صحيح',
            phone: 'يرجى إدخال رقم هاتف صحيح',
            arabicName: 'يرجى إدخال اسم باللغة العربية فقط',
            englishName: 'يرجى إدخال اسم باللغة الإنجليزية فقط',
            number: 'يرجى إدخال رقم صحيح',
            positiveNumber: 'يرجى إدخال رقم موجب',
            minLength: 'يجب أن يكون الطول {min} أحرف على الأقل',
            maxLength: 'يجب أن لا يتجاوز الطول {max} حرف',
            minValue: 'يجب أن تكون القيمة {min} على الأقل',
            maxValue: 'يجب أن لا تتجاوز القيمة {max}',
            barcode: 'يجب أن يكون الرمز الشريطي 6 أرقام',
            password: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            strongPassword: 'كلمة المرور يجب أن تحتوي على 8 أحرف مع أحرف كبيرة وصغيرة وأرقام',
            url: 'يرجى إدخال رابط صحيح',
            date: 'يرجى إدخال تاريخ صحيح',
            futureDate: 'يجب أن يكون التاريخ في المستقبل',
            pastDate: 'يجب أن يكون التاريخ في الماضي'
        };
    }

    // التحقق من حقل واحد
    validateField(value, rules, fieldName = '') {
        const errors = [];
        
        for (const rule of rules) {
            let ruleName, ruleParams = [];
            
            if (typeof rule === 'string') {
                ruleName = rule;
            } else if (typeof rule === 'object') {
                ruleName = rule.name;
                ruleParams = rule.params || [];
            }
            
            if (this.rules[ruleName]) {
                const isValid = this.rules[ruleName](value, ...ruleParams);
                if (!isValid) {
                    let message = this.messages[ruleName] || `خطأ في التحقق من ${ruleName}`;
                    
                    // استبدال المتغيرات في الرسالة
                    ruleParams.forEach((param, index) => {
                        const paramNames = ['min', 'max', 'length'];
                        if (paramNames[index]) {
                            message = message.replace(`{${paramNames[index]}}`, param);
                        }
                    });
                    
                    errors.push({
                        field: fieldName,
                        rule: ruleName,
                        message: message
                    });
                }
            }
        }
        
        return errors;
    }

    // التحقق من نموذج كامل
    validateForm(formData, validationRules) {
        const allErrors = {};
        let isValid = true;
        
        for (const [fieldName, rules] of Object.entries(validationRules)) {
            const fieldValue = formData[fieldName];
            const fieldErrors = this.validateField(fieldValue, rules, fieldName);
            
            if (fieldErrors.length > 0) {
                allErrors[fieldName] = fieldErrors;
                isValid = false;
            }
        }
        
        return {
            isValid,
            errors: allErrors
        };
    }

    // عرض أخطاء التحقق في الواجهة
    displayFieldError(fieldElement, errors) {
        this.clearFieldError(fieldElement);
        
        if (errors && errors.length > 0) {
            fieldElement.classList.add('error');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = errors[0].message;
            
            fieldElement.parentNode.appendChild(errorDiv);
            
            // إضافة تأثير اهتزاز
            fieldElement.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                fieldElement.style.animation = '';
            }, 500);
        }
    }

    // مسح أخطاء الحقل
    clearFieldError(fieldElement) {
        fieldElement.classList.remove('error');
        const existingError = fieldElement.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
    }

    // التحقق الفوري أثناء الكتابة
    setupRealTimeValidation(formElement, validationRules) {
        const fields = formElement.querySelectorAll('input, select, textarea');
        
        fields.forEach(field => {
            const fieldName = field.name || field.id;
            const rules = validationRules[fieldName];
            
            if (rules) {
                // التحقق عند فقدان التركيز
                field.addEventListener('blur', () => {
                    const errors = this.validateField(field.value, rules, fieldName);
                    this.displayFieldError(field, errors);
                });

                // التحقق أثناء الكتابة (مع تأخير)
                let timeout;
                field.addEventListener('input', () => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        const errors = this.validateField(field.value, rules, fieldName);
                        this.displayFieldError(field, errors);
                    }, 500);
                });

                // مسح الأخطاء عند التركيز
                field.addEventListener('focus', () => {
                    this.clearFieldError(field);
                });
            }
        });
    }

    // إضافة قاعدة تحقق مخصصة
    addRule(name, validator, message) {
        this.rules[name] = validator;
        this.messages[name] = message;
    }

    // التحقق من تفرد القيمة
    validateUnique(value, existingValues, fieldName) {
        const isDuplicate = existingValues.some(existing => 
            existing.toString().toLowerCase() === value.toString().toLowerCase()
        );
        
        if (isDuplicate) {
            return [{
                field: fieldName,
                rule: 'unique',
                message: 'هذه القيمة موجودة بالفعل'
            }];
        }
        
        return [];
    }

    // التحقق من تطابق كلمات المرور
    validatePasswordMatch(password, confirmPassword) {
        if (password !== confirmPassword) {
            return [{
                field: 'confirmPassword',
                rule: 'match',
                message: 'كلمة المرور غير متطابقة'
            }];
        }
        return [];
    }

    // تنظيف وتنسيق البيانات
    sanitizeData(data) {
        const sanitized = {};
        
        for (const [key, value] of Object.entries(data)) {
            if (typeof value === 'string') {
                // إزالة المسافات الزائدة
                sanitized[key] = value.trim();
                
                // تنظيف أرقام الهاتف
                if (key.includes('phone') || key.includes('هاتف')) {
                    sanitized[key] = value.replace(/[^\d+]/g, '');
                }
                
                // تنظيف البريد الإلكتروني
                if (key.includes('email') || key.includes('بريد')) {
                    sanitized[key] = value.toLowerCase().trim();
                }
            } else {
                sanitized[key] = value;
            }
        }
        
        return sanitized;
    }
}

// إنشاء مثيل عام للنظام
window.validator = new ValidationSystem();

// قواعد التحقق للنماذج المختلفة
window.validationRules = {
    student: {
        name: ['required', 'arabicName', { name: 'minLength', params: [2] }],
        barcode: ['required', 'barcode'],
        level: ['required'],
        group: ['required'],
        phone: ['phone'],
        parentPhone: ['required', 'phone'],
        fee: ['required', 'positiveNumber']
    },
    
    teacher: {
        name: ['required', 'arabicName', { name: 'minLength', params: [2] }],
        email: ['required', 'email'],
        phone: ['required', 'phone'],
        subjects: ['required'],
        salary: ['positiveNumber']
    },
    
    user: {
        name: ['required', { name: 'minLength', params: [2] }],
        username: ['required', { name: 'minLength', params: [3] }],
        email: ['required', 'email'],
        phone: ['required', 'phone'],
        password: ['required', 'password'],
        role: ['required']
    },
    
    group: {
        name: ['required'],
        level: ['required'],
        maxStudents: ['required', 'positiveNumber', { name: 'maxValue', params: [50] }]
    },
    
    payment: {
        studentId: ['required'],
        amount: ['required', 'positiveNumber'],
        month: ['required'],
        paymentDate: ['required', 'date']
    }
};

// تصدير النظام
export { ValidationSystem, validationRules };

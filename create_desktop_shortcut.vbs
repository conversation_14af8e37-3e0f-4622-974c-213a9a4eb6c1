' إنشاء اختصار على سطح المكتب لنظام إدارة مؤسسة النور التربوي
Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\نظام إدارة مؤسسة النور التربوي.lnk")

' الحصول على المسار الحالي
strCurrentPath = CreateObject("Scripting.FileSystemObject").GetAbsolutePathName(".")

' إعداد خصائص الاختصار
oShellLink.TargetPath = strCurrentPath & "\index.html"
oShellLink.WorkingDirectory = strCurrentPath
oShellLink.Description = "نظام إدارة مؤسسة النور التربوي للتعليم الخصوصي - الإصدار 2.0"
oShellLink.WindowStyle = 1

' محاولة تعيين أيقونة مخصصة (إذا كانت متوفرة)
' يمكنك تغيير هذا المسار إلى أيقونة مخصصة
oShellLink.IconLocation = "shell32.dll,21"

' حفظ الاختصار
oShellLink.Save

' إنشاء اختصار للتشغيل المتقدم أيضاً
Set oShellLink2 = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\تشغيل متقدم - نظام النور التربوي.lnk")
oShellLink2.TargetPath = strCurrentPath & "\تشغيل_متقدم.bat"
oShellLink2.WorkingDirectory = strCurrentPath
oShellLink2.Description = "تشغيل متقدم مع خيارات إضافية لنظام إدارة مؤسسة النور التربوي"
oShellLink2.WindowStyle = 1
oShellLink2.IconLocation = "shell32.dll,25"
oShellLink2.Save

' إنشاء اختصار للاختبار السريع
Set oShellLink3 = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\اختبار سريع - نظام النور التربوي.lnk")
oShellLink3.TargetPath = strCurrentPath & "\quick-test.html"
oShellLink3.WorkingDirectory = strCurrentPath
oShellLink3.Description = "اختبار سريع وتشخيص شامل لنظام إدارة مؤسسة النور التربوي"
oShellLink3.WindowStyle = 1
oShellLink3.IconLocation = "shell32.dll,23"
oShellLink3.Save

' عرض رسالة تأكيد
MsgBox "تم إنشاء الاختصارات على سطح المكتب بنجاح!" & vbCrLf & vbCrLf & _
       "الاختصارات المُنشأة:" & vbCrLf & _
       "• نظام إدارة مؤسسة النور التربوي" & vbCrLf & _
       "• تشغيل متقدم - نظام النور التربوي" & vbCrLf & _
       "• اختبار سريع - نظام النور التربوي", _
       vbInformation, "نظام إدارة مؤسسة النور التربوي"

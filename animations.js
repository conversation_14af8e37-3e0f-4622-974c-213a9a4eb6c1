// ===================================================================
//                    نظام الرسوم المتحركة والانتقالات
// ===================================================================

class AnimationSystem {
    constructor() {
        this.observers = new Map();
        this.init();
    }

    // تهيئة النظام
    init() {
        this.setupStyles();
        this.setupIntersectionObserver();
        this.setupPageTransitions();
    }

    // إعداد الأنماط
    setupStyles() {
        if (document.getElementById('animation-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'animation-styles';
        styles.textContent = `
            /* الرسوم المتحركة الأساسية */
            .animate-fade-in {
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.6s ease-out;
            }

            .animate-fade-in.visible {
                opacity: 1;
                transform: translateY(0);
            }

            .animate-slide-in-left {
                opacity: 0;
                transform: translateX(-50px);
                transition: all 0.6s ease-out;
            }

            .animate-slide-in-left.visible {
                opacity: 1;
                transform: translateX(0);
            }

            .animate-slide-in-right {
                opacity: 0;
                transform: translateX(50px);
                transition: all 0.6s ease-out;
            }

            .animate-slide-in-right.visible {
                opacity: 1;
                transform: translateX(0);
            }

            .animate-scale-in {
                opacity: 0;
                transform: scale(0.8);
                transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            }

            .animate-scale-in.visible {
                opacity: 1;
                transform: scale(1);
            }

            .animate-bounce-in {
                opacity: 0;
                transform: scale(0.3);
                transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            }

            .animate-bounce-in.visible {
                opacity: 1;
                transform: scale(1);
            }

            .animate-rotate-in {
                opacity: 0;
                transform: rotate(-180deg) scale(0.5);
                transition: all 0.8s ease-out;
            }

            .animate-rotate-in.visible {
                opacity: 1;
                transform: rotate(0deg) scale(1);
            }

            /* رسوم متحركة للنصوص */
            .animate-text-reveal {
                overflow: hidden;
                position: relative;
            }

            .animate-text-reveal::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #007bff;
                transform: translateX(-100%);
                transition: transform 0.8s ease-out;
            }

            .animate-text-reveal.visible::after {
                transform: translateX(100%);
            }

            /* رسوم متحركة للأزرار */
            .btn-animated {
                position: relative;
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .btn-animated::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s ease;
            }

            .btn-animated:hover::before {
                left: 100%;
            }

            .btn-animated:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }

            /* رسوم متحركة للبطاقات */
            .card-animated {
                transition: all 0.3s ease;
                transform-origin: center;
            }

            .card-animated:hover {
                transform: translateY(-5px) scale(1.02);
                box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            }

            /* رسوم متحركة للقوائم */
            .list-item-animated {
                opacity: 0;
                transform: translateX(-20px);
                transition: all 0.4s ease-out;
            }

            .list-item-animated.visible {
                opacity: 1;
                transform: translateX(0);
            }

            /* تأثيرات التحويم */
            .hover-lift {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .hover-lift:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            }

            .hover-glow {
                transition: box-shadow 0.3s ease;
            }

            .hover-glow:hover {
                box-shadow: 0 0 20px rgba(0,123,255,0.3);
            }

            .hover-rotate {
                transition: transform 0.3s ease;
            }

            .hover-rotate:hover {
                transform: rotate(5deg);
            }

            /* رسوم متحركة للنماذج */
            .form-field-animated {
                position: relative;
                margin-bottom: 25px;
            }

            .form-field-animated input,
            .form-field-animated select,
            .form-field-animated textarea {
                width: 100%;
                padding: 15px 12px 5px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-size: 16px;
                transition: all 0.3s ease;
                background: transparent;
            }

            .form-field-animated label {
                position: absolute;
                top: 15px;
                right: 12px;
                font-size: 16px;
                color: #6c757d;
                transition: all 0.3s ease;
                pointer-events: none;
                background: white;
                padding: 0 5px;
            }

            .form-field-animated input:focus,
            .form-field-animated select:focus,
            .form-field-animated textarea:focus,
            .form-field-animated input:not(:placeholder-shown),
            .form-field-animated select:not([value=""]),
            .form-field-animated textarea:not(:placeholder-shown) {
                border-color: #007bff;
                outline: none;
            }

            .form-field-animated input:focus + label,
            .form-field-animated select:focus + label,
            .form-field-animated textarea:focus + label,
            .form-field-animated input:not(:placeholder-shown) + label,
            .form-field-animated select:not([value=""]) + label,
            .form-field-animated textarea:not(:placeholder-shown) + label {
                top: -8px;
                font-size: 12px;
                color: #007bff;
                font-weight: bold;
            }

            /* رسوم متحركة للجداول */
            .table-row-animated {
                opacity: 0;
                transform: translateY(10px);
                transition: all 0.3s ease-out;
            }

            .table-row-animated.visible {
                opacity: 1;
                transform: translateY(0);
            }

            .table-row-animated:hover {
                background: #f8f9fa;
                transform: scale(1.01);
            }

            /* رسوم متحركة للإحصائيات */
            .stat-counter {
                font-size: 2rem;
                font-weight: bold;
                color: #007bff;
            }

            .stat-progress {
                width: 100%;
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                overflow: hidden;
                margin-top: 10px;
            }

            .stat-progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #0056b3);
                border-radius: 4px;
                width: 0%;
                transition: width 2s ease-out;
            }

            /* تأثيرات الصفحة */
            .page-transition {
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.5s ease-out;
            }

            .page-transition.loaded {
                opacity: 1;
                transform: translateY(0);
            }

            /* رسوم متحركة للأيقونات */
            .icon-spin {
                animation: spin 2s linear infinite;
            }

            .icon-bounce {
                animation: bounce 1s ease-in-out infinite;
            }

            .icon-pulse {
                animation: pulse 2s ease-in-out infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% {
                    transform: translate3d(0,0,0);
                }
                40%, 43% {
                    transform: translate3d(0,-30px,0);
                }
                70% {
                    transform: translate3d(0,-15px,0);
                }
                90% {
                    transform: translate3d(0,-4px,0);
                }
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }

            /* تأثيرات التركيز */
            .focus-ring {
                transition: box-shadow 0.3s ease;
            }

            .focus-ring:focus {
                box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
                outline: none;
            }

            /* رسوم متحركة للتنبيهات */
            .alert-animated {
                animation: slideInDown 0.5s ease-out;
            }

            @keyframes slideInDown {
                from {
                    transform: translateY(-100%);
                    opacity: 0;
                }
                to {
                    transform: translateY(0);
                    opacity: 1;
                }
            }

            /* تأثيرات التحميل */
            .loading-shimmer {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: shimmer 1.5s infinite;
            }

            @keyframes shimmer {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            /* تحسينات الأداء */
            .will-change-transform {
                will-change: transform;
            }

            .will-change-opacity {
                will-change: opacity;
            }

            .gpu-accelerated {
                transform: translateZ(0);
                backface-visibility: hidden;
                perspective: 1000px;
            }
        `;
        document.head.appendChild(styles);
    }

    // إعداد مراقب التقاطع
    setupIntersectionObserver() {
        const options = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    
                    // تأثيرات خاصة للعناصر المختلفة
                    if (entry.target.classList.contains('stat-counter')) {
                        this.animateCounter(entry.target);
                    }
                    
                    if (entry.target.classList.contains('stat-progress-bar')) {
                        this.animateProgressBar(entry.target);
                    }
                    
                    if (entry.target.classList.contains('list-item-animated')) {
                        this.animateListItem(entry.target);
                    }
                }
            });
        }, options);
    }

    // إعداد انتقالات الصفحة
    setupPageTransitions() {
        document.addEventListener('DOMContentLoaded', () => {
            document.body.classList.add('page-transition');
            
            setTimeout(() => {
                document.body.classList.add('loaded');
            }, 100);
        });
    }

    // تطبيق رسوم متحركة على عنصر
    animate(element, animationType, delay = 0) {
        if (!element) return;

        element.classList.add(`animate-${animationType}`);
        
        if (delay > 0) {
            element.style.animationDelay = `${delay}ms`;
        }

        this.intersectionObserver.observe(element);
    }

    // تطبيق رسوم متحركة على مجموعة عناصر
    animateGroup(elements, animationType, staggerDelay = 100) {
        elements.forEach((element, index) => {
            this.animate(element, animationType, index * staggerDelay);
        });
    }

    // رسوم متحركة للعداد
    animateCounter(element) {
        const target = parseInt(element.textContent) || 0;
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const updateCounter = () => {
            current += increment;
            if (current < target) {
                element.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                element.textContent = target;
            }
        };

        updateCounter();
    }

    // رسوم متحركة لشريط التقدم
    animateProgressBar(element) {
        const targetWidth = element.getAttribute('data-width') || '100%';
        
        setTimeout(() => {
            element.style.width = targetWidth;
        }, 200);
    }

    // رسوم متحركة لعناصر القائمة
    animateListItem(element) {
        const siblings = Array.from(element.parentNode.children);
        const index = siblings.indexOf(element);
        
        setTimeout(() => {
            element.classList.add('visible');
        }, index * 100);
    }

    // تأثير الاهتزاز
    shake(element) {
        element.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    }

    // تأثير النبض
    pulse(element) {
        element.classList.add('icon-pulse');
        setTimeout(() => {
            element.classList.remove('icon-pulse');
        }, 2000);
    }

    // تأثير التوهج
    glow(element, duration = 1000) {
        element.style.boxShadow = '0 0 20px rgba(0,123,255,0.5)';
        element.style.transition = 'box-shadow 0.3s ease';
        
        setTimeout(() => {
            element.style.boxShadow = '';
        }, duration);
    }

    // رسوم متحركة للنماذج
    animateForm(form) {
        const fields = form.querySelectorAll('.form-group');
        
        fields.forEach((field, index) => {
            field.classList.add('form-field-animated');
            this.animate(field, 'fade-in', index * 100);
        });
    }

    // رسوم متحركة للجداول
    animateTable(table) {
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach((row, index) => {
            row.classList.add('table-row-animated');
            this.animate(row, 'fade-in', index * 50);
        });
    }

    // رسوم متحركة للبطاقات
    animateCards(container) {
        const cards = container.querySelectorAll('.card, .stat-card, .settings-card');
        
        cards.forEach((card, index) => {
            card.classList.add('card-animated');
            this.animate(card, 'scale-in', index * 150);
        });
    }

    // تطبيق رسوم متحركة تلقائية
    autoAnimate() {
        // رسوم متحركة للعناصر الموجودة
        const fadeElements = document.querySelectorAll('.auto-fade-in');
        fadeElements.forEach(el => this.animate(el, 'fade-in'));

        const slideLeftElements = document.querySelectorAll('.auto-slide-left');
        slideLeftElements.forEach(el => this.animate(el, 'slide-in-left'));

        const slideRightElements = document.querySelectorAll('.auto-slide-right');
        slideRightElements.forEach(el => this.animate(el, 'slide-in-right'));

        const scaleElements = document.querySelectorAll('.auto-scale-in');
        scaleElements.forEach(el => this.animate(el, 'scale-in'));

        // رسوم متحركة للنماذج
        const forms = document.querySelectorAll('form');
        forms.forEach(form => this.animateForm(form));

        // رسوم متحركة للجداول
        const tables = document.querySelectorAll('table');
        tables.forEach(table => this.animateTable(table));

        // رسوم متحركة للبطاقات
        const cardContainers = document.querySelectorAll('.stats-grid, .settings-container');
        cardContainers.forEach(container => this.animateCards(container));
    }

    // إضافة تأثيرات التحويم
    addHoverEffects() {
        // تأثيرات الأزرار
        const buttons = document.querySelectorAll('button, .btn');
        buttons.forEach(btn => {
            btn.classList.add('btn-animated');
        });

        // تأثيرات البطاقات
        const cards = document.querySelectorAll('.card, .stat-card');
        cards.forEach(card => {
            card.classList.add('hover-lift');
        });

        // تأثيرات الروابط
        const links = document.querySelectorAll('a');
        links.forEach(link => {
            link.classList.add('hover-glow');
        });
    }
}

// إنشاء مثيل عام للنظام
window.animationSystem = new AnimationSystem();

// تطبيق الرسوم المتحركة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        window.animationSystem.autoAnimate();
        window.animationSystem.addHoverEffects();
        window.animationSystem.applyAllEnhancements();
    }, 100);
});

    // تحسين التنقل بين الصفحات
    enhanceNavigation() {
        const navLinks = document.querySelectorAll('nav a');

        navLinks.forEach(link => {
            // إضافة تأثير التحميل عند النقر
            link.addEventListener('click', (e) => {
                // تجاهل الروابط الخارجية
                if (link.href.startsWith('http') && !link.href.includes(window.location.hostname)) {
                    return;
                }

                // إضافة تأثير التحميل
                const loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'page-transition-overlay';
                loadingOverlay.innerHTML = `
                    <div class="page-transition-content">
                        <div class="loading-spinner"></div>
                        <p>جاري التحميل...</p>
                    </div>
                `;

                document.body.appendChild(loadingOverlay);

                // إزالة التأثير بعد فترة قصيرة
                setTimeout(() => {
                    if (loadingOverlay.parentNode) {
                        loadingOverlay.remove();
                    }
                }, 500);
            });

            // تأثير التمرير
            link.addEventListener('mouseenter', () => {
                this.animate(link, 'pulse', 0);
            });
        });
    }

    // تحسين تأثيرات الأزرار
    enhanceButtons() {
        const buttons = document.querySelectorAll('button, .btn');

        buttons.forEach(button => {
            // تأثير الضغط
            button.addEventListener('mousedown', () => {
                button.style.transform = 'scale(0.95)';
            });

            button.addEventListener('mouseup', () => {
                button.style.transform = 'scale(1)';
            });

            button.addEventListener('mouseleave', () => {
                button.style.transform = 'scale(1)';
            });

            // تأثير التمرير
            button.addEventListener('mouseenter', () => {
                if (!button.disabled) {
                    this.glow(button, 300);
                }
            });
        });
    }

    // تحسين تأثيرات النماذج
    enhanceForms() {
        const inputs = document.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            // تأثير التركيز
            input.addEventListener('focus', () => {
                this.animate(input, 'glow', 0);
                input.parentElement?.classList.add('focused');
            });

            input.addEventListener('blur', () => {
                input.parentElement?.classList.remove('focused');
            });

            // تأثير التحقق من الصحة
            input.addEventListener('invalid', () => {
                this.shake(input);
                input.classList.add('error');
            });

            input.addEventListener('input', () => {
                input.classList.remove('error');
            });
        });
    }

    // تحسين تأثيرات الجداول
    enhanceTables() {
        const tables = document.querySelectorAll('table');

        tables.forEach(table => {
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach((row, index) => {
                // تأثير الظهور المتدرج
                setTimeout(() => {
                    this.animate(row, 'fade-in', 0);
                }, index * 50);

                // تأثير التمرير
                row.addEventListener('mouseenter', () => {
                    row.style.transform = 'translateX(5px)';
                    row.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                });

                row.addEventListener('mouseleave', () => {
                    row.style.transform = 'translateX(0)';
                    row.style.boxShadow = 'none';
                });
            });
        });
    }

    // تحسين تأثيرات البطاقات
    enhanceCards() {
        const cards = document.querySelectorAll('.card, .stat-card, .summary-card, .category-card');

        cards.forEach((card, index) => {
            // تأثير الظهور المتدرج
            setTimeout(() => {
                this.animate(card, 'fade-in', 0);
            }, index * 100);

            // تأثير التمرير
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
                card.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });
        });
    }

    // تطبيق جميع التحسينات
    applyAllEnhancements() {
        this.enhanceNavigation();
        this.enhanceButtons();
        this.enhanceForms();
        this.enhanceTables();
        this.enhanceCards();
    }
}

// إضافة أنماط CSS للتحسينات
const enhancementStyles = document.createElement('style');
enhancementStyles.textContent = `
    /* تأثيرات التنقل */
    .page-transition-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        animation: fadeIn 0.3s ease-out;
    }

    .page-transition-content {
        text-align: center;
        color: #007bff;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e3f2fd;
        border-top: 4px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* تحسينات النماذج */
    .form-group.focused {
        transform: scale(1.02);
    }

    .form-group input.error,
    .form-group select.error,
    .form-group textarea.error {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    /* تحسينات الأزرار */
    button, .btn {
        transition: all 0.3s ease;
    }

    button:hover, .btn:hover {
        transform: translateY(-2px);
    }

    /* تحسينات الجداول */
    table tbody tr {
        transition: all 0.3s ease;
    }

    /* تحسينات البطاقات */
    .card, .stat-card, .summary-card, .category-card {
        transition: all 0.3s ease;
    }

    /* تأثيرات إضافية */
    .smooth-transition {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hover-lift:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .hover-glow:hover {
        box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
    }
`;

document.head.appendChild(enhancementStyles);

// اختصارات سريعة
window.animate = (element, type, delay) => window.animationSystem.animate(element, type, delay);
window.animateGroup = (elements, type, delay) => window.animationSystem.animateGroup(elements, type, delay);
window.shake = (element) => window.animationSystem.shake(element);
window.pulse = (element) => window.animationSystem.pulse(element);
window.glow = (element, duration) => window.animationSystem.glow(element, duration);

// تصدير النظام
export { AnimationSystem };

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التلاميذ - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <img src="logo" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html" class="active">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="expenses.html">إدارة النفقات</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="profile.html">الملف الشخصي</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="student-management">
            <h2>إدارة التلاميذ</h2>
            <form id="add-student-form">
                <input type="text" id="student-name" placeholder="اسم التلميذ" required>
                <select id="student-level" required>
                    <option value="">اختر المستوى</option>
                </select>
                <select id="student-group" required>
                    <option value="">اختر الفوج</option>
                </select>
                <input type="number" id="student-fee" placeholder="المستحقات الشهرية" required>
                <input type="tel" id="student-phone" placeholder="رقم هاتف ولي الأمر" required>
                <div class="form-full-width">
                    <label for="student-transport">
                        <input type="checkbox" id="student-transport"> يستفيد من خدمة النقل
                    </label>
                </div>
                <div class="form-full-width" id="transport-fee-container" style="display: none;">
                    <input type="number" id="student-transport-fee" placeholder="رسوم النقل الشهرية">
                </div>

                <div class="form-section annual-fees">
                    <h4>المصاريف السنوية</h4>
                    <div class="annual-fees-grid">
                        <div class="fee-item">
                            <label for="registration-fee">مصاريف التسجيل:</label>
                            <input type="number" id="registration-fee" placeholder="مصاريف التسجيل (سنوية)">
                        </div>
                        <div class="fee-item">
                            <label for="books-fee">مصاريف الكتب المدرسية:</label>
                            <input type="number" id="books-fee" placeholder="مصاريف الكتب (سنوية)">
                        </div>
                    </div>
                </div>
                <div class="form-full-width">
                    <label for="student-picture">الصورة الشخصية:</label>
                    <input type="file" id="student-picture" accept="image/*">
                </div>
                <button type="submit">إضافة تلميذ</button>
            </form>
            
            <div class="toolbar">
                <input type="text" id="search-name" placeholder="ابحث بالاسم...">
                <select id="search-level">
                    <option value="">ابحث بالمستوى...</option>
                </select>
                <input type="text" id="search-barcode" placeholder="ابحث بالرقم التسلسلي...">
                <button id="import-excel-btn" class="secondary-btn">استيراد من Excel</button>
                <button id="download-template-btn" class="secondary-btn">تحميل نموذج Excel</button>
                <input type="file" id="excel-file-input" style="display: none;" accept=".xlsx, .xls">
            </div>

            <div id="student-list-container">
                <table id="student-table">
                    <thead>
                        <tr>
                            <th>الصورة الشخصية</th>
                            <th>الرقم التسلسلي</th>
                            <th>الاسم الكامل</th>
                            <th>المستوى</th>
                            <th>الفوج</th>
                            <th>المستحقات الشهرية</th>
                            <th>النقل</th>
                            <th>إجمالي المدفوع</th>
                            <th>إجمالي الباقي</th>
                            <th>هاتف الولي</th>
                            <th>الباركود</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Student rows will be inserted here -->
                    </tbody>
                </table>
            </div>
            <button id="print-students-btn">طباعة قائمة التلاميذ</button>
        </section>
    </main>

    <!-- نافذة إدارة الدفعات -->
    <div id="payments-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إدارة الدفعات الشهرية</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="student-info"></div>
                <div class="payments-tabs">
                    <button class="tab-btn active" data-tab="monthly">الدفعات الشهرية</button>
                    <button class="tab-btn" data-tab="additional">الواجبات الإضافية</button>
                    <button class="tab-btn" data-tab="annual">المصاريف السنوية</button>
                </div>
                <div id="monthly-payments" class="tab-content active">
                    <div id="payments-grid"></div>
                </div>
                <div id="additional-payments" class="tab-content">
                    <div class="additional-fees-section">
                        <h4>إضافة واجب إضافي</h4>
                        <form id="add-additional-fee-form">
                            <input type="text" id="fee-description" placeholder="وصف الواجب" required>
                            <input type="number" id="fee-amount" placeholder="المبلغ" required>
                            <input type="date" id="fee-due-date" required>
                            <button type="submit">إضافة</button>
                        </form>
                    </div>
                    <div id="additional-fees-list"></div>
                </div>
                <div id="annual-payments" class="tab-content">
                    <div class="annual-fees-section">
                        <h4>المصاريف السنوية</h4>
                        <div class="annual-fees-grid">
                            <div class="annual-fee-item">
                                <div class="fee-header">
                                    <h5>مصاريف التسجيل</h5>
                                    <div id="registration-fee-status" class="fee-status unpaid">غير مدفوع</div>
                                </div>
                                <div class="fee-amount" id="registration-fee-amount">0 DHS</div>
                                <button class="pay-annual-fee-btn" data-type="registration">دفع مصاريف التسجيل</button>
                            </div>
                            <div class="annual-fee-item">
                                <div class="fee-header">
                                    <h5>مصاريف الكتب المدرسية</h5>
                                    <div id="books-fee-status" class="fee-status unpaid">غير مدفوع</div>
                                </div>
                                <div class="fee-amount" id="books-fee-amount">0 DHS</div>
                                <button class="pay-annual-fee-btn" data-type="books">دفع مصاريف الكتب</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل بيانات التلميذ -->
    <div id="edit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل بيانات التلميذ</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="edit-student-form">
                    <input type="text" id="edit-student-name" placeholder="اسم التلميذ" required>
                    <select id="edit-student-level" required>
                        <option value="">اختر المستوى</option>
                    </select>
                    <select id="edit-student-group" required>
                        <option value="">اختر الفوج</option>
                    </select>
                    <input type="number" id="edit-student-fee" placeholder="المستحقات الشهرية" required>
                    <input type="tel" id="edit-student-phone" placeholder="رقم هاتف ولي الأمر" required>
                    <div class="form-full-width">
                        <label for="edit-student-transport">
                            <input type="checkbox" id="edit-student-transport"> يستفيد من خدمة النقل
                        </label>
                    </div>
                    <div class="form-full-width" id="edit-transport-fee-container" style="display: none;">
                        <input type="number" id="edit-student-transport-fee" placeholder="رسوم النقل الشهرية">
                    </div>

                    <div class="form-section annual-fees">
                        <h4>المصاريف السنوية</h4>
                        <div class="annual-fees-grid">
                            <div class="fee-item">
                                <label for="edit-registration-fee">مصاريف التسجيل:</label>
                                <input type="number" id="edit-registration-fee" placeholder="مصاريف التسجيل (سنوية)">
                            </div>
                            <div class="fee-item">
                                <label for="edit-books-fee">مصاريف الكتب المدرسية:</label>
                                <input type="number" id="edit-books-fee" placeholder="مصاريف الكتب (سنوية)">
                            </div>
                        </div>
                    </div>

                    <div class="form-full-width">
                        <label for="edit-student-picture">الصورة الشخصية:</label>
                        <input type="file" id="edit-student-picture" accept="image/*">
                        <div id="current-picture-preview"></div>
                    </div>
                    <div class="modal-actions">
                        <button type="submit">حفظ التعديلات</button>
                        <button type="button" class="cancel-btn">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="validation.js"></script>
    <script src="notifications.js"></script>
    <script src="loading.js"></script>
    <script src="animations.js"></script>
    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
</body>
</html>
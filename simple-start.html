<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>نظام النور التربوي - بداية بسيطة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5rem;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 1.2rem;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .card:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,123,255,0.2);
        }
        .card-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        .card-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .card-desc {
            color: #666;
            margin-bottom: 20px;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s ease;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; }
        .status-bar {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏫 مؤسسة النور التربوي</h1>
            <p>نظام إدارة المؤسسات التعليمية</p>
        </div>

        <div class="status-bar">
            <strong>حالة النظام:</strong> <span id="system-status">جاري التحميل...</span>
        </div>

        <div class="quick-stats">
            <div class="stat-item">
                <span class="stat-number" id="students-count">0</span>
                <span class="stat-label">التلاميذ</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="teachers-count">0</span>
                <span class="stat-label">الأساتذة</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="groups-count">0</span>
                <span class="stat-label">المجموعات</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="expenses-count">0</span>
                <span class="stat-label">النفقات</span>
            </div>
        </div>

        <div class="grid">
            <div class="card" onclick="openStudents()">
                <div class="card-icon">👥</div>
                <div class="card-title">إدارة التلاميذ</div>
                <div class="card-desc">إضافة وتعديل وإدارة بيانات التلاميذ</div>
                <button class="btn">فتح</button>
            </div>

            <div class="card" onclick="openTeachers()">
                <div class="card-icon">👨‍🏫</div>
                <div class="card-title">إدارة الأساتذة</div>
                <div class="card-desc">إدارة بيانات الأساتذة والمواد</div>
                <button class="btn">فتح</button>
            </div>

            <div class="card" onclick="openFinancial()">
                <div class="card-icon">💰</div>
                <div class="card-title">النظام المالي</div>
                <div class="card-desc">إدارة المدفوعات والرسوم</div>
                <button class="btn">فتح</button>
            </div>

            <div class="card" onclick="openExpenses()">
                <div class="card-icon">📊</div>
                <div class="card-title">إدارة النفقات</div>
                <div class="card-desc">تسجيل وتتبع النفقات اليومية</div>
                <button class="btn">فتح</button>
            </div>

            <div class="card" onclick="addStudent()">
                <div class="card-icon">➕</div>
                <div class="card-title">إضافة تلميذ</div>
                <div class="card-desc">إضافة تلميذ جديد بسرعة</div>
                <button class="btn btn-success">إضافة</button>
            </div>

            <div class="card" onclick="openTest()">
                <div class="card-icon">🧪</div>
                <div class="card-title">اختبار النظام</div>
                <div class="card-desc">فحص وتشخيص النظام</div>
                <button class="btn btn-warning">اختبار</button>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 2px solid #e9ecef;">
            <button class="btn btn-danger" onclick="resetSystem()">🔄 إعادة تعيين النظام</button>
            <button class="btn btn-warning" onclick="openEmergencyFix()">🚨 إصلاح طارئ</button>
        </div>
    </div>

    <script>
        // تحميل البيانات وعرض الإحصائيات
        function loadStats() {
            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                const teachers = JSON.parse(localStorage.getItem('sbea_teachers') || '[]');
                const groups = JSON.parse(localStorage.getItem('sbea_groups') || '[]');
                const expenses = JSON.parse(localStorage.getItem('sbea_expenses') || '[]');

                document.getElementById('students-count').textContent = students.length;
                document.getElementById('teachers-count').textContent = teachers.length;
                document.getElementById('groups-count').textContent = groups.length;
                document.getElementById('expenses-count').textContent = expenses.length;

                document.getElementById('system-status').innerHTML = '<span style="color: green;">✅ يعمل بشكل طبيعي</span>';
            } catch (error) {
                document.getElementById('system-status').innerHTML = '<span style="color: red;">❌ خطأ في البيانات</span>';
                console.error('خطأ في تحميل البيانات:', error);
            }
        }

        // فتح الصفحات
        function openStudents() {
            window.open('students.html', '_blank');
        }

        function openTeachers() {
            window.open('teachers.html', '_blank');
        }

        function openFinancial() {
            window.open('financial.html', '_blank');
        }

        function openExpenses() {
            window.open('expenses.html', '_blank');
        }

        function openTest() {
            window.open('quick-test.html', '_blank');
        }

        function openEmergencyFix() {
            window.open('emergency-fix.html', '_blank');
        }

        // إضافة تلميذ سريع
        function addStudent() {
            const name = prompt('اسم التلميذ:');
            if (!name) return;

            const level = prompt('المستوى الدراسي:');
            if (!level) return;

            const group = prompt('الفوج:');
            if (!group) return;

            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                const lastId = parseInt(localStorage.getItem('sbea_last_student_id') || '0');
                const newId = lastId + 1;

                const newStudent = {
                    id: newId,
                    barcode: newId.toString(),
                    name: name,
                    level: level,
                    group: group,
                    phone: '',
                    monthlyFee: 0,
                    registrationFee: 0,
                    transportFee: 0,
                    payments: {},
                    createdAt: new Date().toISOString()
                };

                students.push(newStudent);
                localStorage.setItem('sbea_students', JSON.stringify(students));
                localStorage.setItem('sbea_last_student_id', newId.toString());

                alert('تم إضافة التلميذ بنجاح!\nالرقم التسلسلي: ' + newId);
                loadStats();
            } catch (error) {
                alert('حدث خطأ في إضافة التلميذ: ' + error.message);
            }
        }

        // إعادة تعيين النظام
        function resetSystem() {
            if (confirm('هل أنت متأكد من إعادة تعيين النظام؟ سيتم حذف جميع البيانات!')) {
                try {
                    // مسح جميع البيانات
                    Object.keys(localStorage).forEach(key => {
                        if (key.startsWith('sbea_')) {
                            localStorage.removeItem(key);
                        }
                    });

                    // إنشاء بيانات افتراضية
                    localStorage.setItem('sbea_students', '[]');
                    localStorage.setItem('sbea_teachers', '[]');
                    localStorage.setItem('sbea_groups', '[]');
                    localStorage.setItem('sbea_expenses', '[]');
                    localStorage.setItem('sbea_last_student_id', '0');

                    alert('تم إعادة تعيين النظام بنجاح!');
                    loadStats();
                } catch (error) {
                    alert('حدث خطأ في إعادة التعيين: ' + error.message);
                }
            }
        }

        // تحميل البيانات عند بدء الصفحة
        window.onload = function() {
            loadStats();
        };
    </script>
</body>
</html>

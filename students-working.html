<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التلاميذ - نظام يعمل 100%</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            direction: rtl;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .nav-link {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .nav-link:hover {
            background: white;
            color: #667eea;
            border-color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }

        .actions-section {
            background: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .students-section {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .search-bar {
            display: flex;
            gap: 15px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .search-input {
            flex: 1;
            min-width: 200px;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
        }

        .search-input:focus {
            outline: none;
            border-color: #007bff;
        }

        .students-table {
            width: 100%;
            border-collapse: collapse;
        }

        .students-table th {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: bold;
        }

        .students-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #e9ecef;
        }

        .students-table tr:hover {
            background: #f8f9fa;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin: 0 auto;
        }

        .action-btn {
            padding: 8px 12px;
            margin: 2px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007bff;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .no-data {
            text-align: center;
            padding: 50px;
            color: #666;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .search-bar {
                flex-direction: column;
            }

            .students-table {
                font-size: 14px;
            }

            .students-table th,
            .students-table td {
                padding: 10px 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏫 إدارة التلاميذ</h1>
            <p>نظام محسن وموثوق 100%</p>
            <div class="nav-links">
                <a href="index.html" class="nav-link">🏠 الرئيسية</a>
                <a href="teachers.html" class="nav-link">👨‍🏫 الأساتذة</a>
                <a href="financial.html" class="nav-link">💰 المالية</a>
                <a href="expenses.html" class="nav-link">📊 النفقات</a>
            </div>
        </div>

        <!-- Alert Messages -->
        <div id="alert-container"></div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" style="color: #007bff;" id="total-students">0</div>
                <div class="stat-label">إجمالي التلاميذ</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #28a745;" id="total-fees">0 DH</div>
                <div class="stat-label">إجمالي الرسوم</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #17a2b8;" id="total-paid">0 DH</div>
                <div class="stat-label">المدفوع</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #dc3545;" id="total-remaining">0 DH</div>
                <div class="stat-label">المتبقي</div>
            </div>
        </div>

        <!-- Actions -->
        <div class="actions-section">
            <h3>الإجراءات السريعة</h3>
            <div class="actions-grid">
                <button class="btn btn-primary" onclick="openAddStudentModal()">
                    ➕ إضافة تلميذ جديد
                </button>
                <button class="btn btn-success" onclick="refreshData()">
                    🔄 تحديث البيانات
                </button>
                <button class="btn btn-warning" onclick="exportData()">
                    📤 تصدير البيانات
                </button>
                <button class="btn btn-danger" onclick="resetSystem()">
                    🔧 إعادة تعيين النظام
                </button>
            </div>
        </div>

        <!-- Students Section -->
        <div class="students-section">
            <div class="section-header">
                <h3>قائمة التلاميذ</h3>
                <div class="search-bar">
                    <input type="text" class="search-input" id="search-name" placeholder="البحث بالاسم...">
                    <input type="text" class="search-input" id="search-barcode" placeholder="الرقم التسلسلي...">
                    <select class="search-input" id="search-level">
                        <option value="">جميع المستويات</option>
                        <option value="الأول ابتدائي">الأول ابتدائي</option>
                        <option value="الثاني ابتدائي">الثاني ابتدائي</option>
                        <option value="الثالث ابتدائي">الثالث ابتدائي</option>
                        <option value="الرابع ابتدائي">الرابع ابتدائي</option>
                        <option value="الخامس ابتدائي">الخامس ابتدائي</option>
                        <option value="السادس ابتدائي">السادس ابتدائي</option>
                    </select>
                    <button class="btn btn-primary" onclick="applyFilters()">🔍 بحث</button>
                </div>
            </div>

            <div style="overflow-x: auto;">
                <table class="students-table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>الرقم</th>
                            <th>الاسم</th>
                            <th>المستوى</th>
                            <th>الفوج</th>
                            <th>الرسوم</th>
                            <th>المدفوع</th>
                            <th>المتبقي</th>
                            <th>الهاتف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="students-table-body">
                        <tr>
                            <td colspan="10" class="no-data">
                                لا توجد بيانات تلاميذ. انقر على "إضافة تلميذ جديد" للبدء.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add Student Modal -->
    <div id="add-student-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إضافة تلميذ جديد</h3>
                <button class="close-btn" onclick="closeModal('add-student-modal')">&times;</button>
            </div>
            <form id="add-student-form">
                <div class="form-group">
                    <label for="student-name">اسم التلميذ *</label>
                    <input type="text" id="student-name" required>
                </div>
                <div class="form-group">
                    <label for="student-level">المستوى الدراسي *</label>
                    <select id="student-level" required>
                        <option value="">اختر المستوى</option>
                        <option value="الأول ابتدائي">الأول ابتدائي</option>
                        <option value="الثاني ابتدائي">الثاني ابتدائي</option>
                        <option value="الثالث ابتدائي">الثالث ابتدائي</option>
                        <option value="الرابع ابتدائي">الرابع ابتدائي</option>
                        <option value="الخامس ابتدائي">الخامس ابتدائي</option>
                        <option value="السادس ابتدائي">السادس ابتدائي</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="student-group">الفوج *</label>
                    <select id="student-group" required>
                        <option value="">اختر الفوج</option>
                        <option value="أ">أ</option>
                        <option value="ب">ب</option>
                        <option value="ج">ج</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="student-fee">الرسوم الشهرية (درهم)</label>
                    <input type="number" id="student-fee" min="0" step="0.01" value="0">
                </div>
                <div class="form-group">
                    <label for="student-phone">رقم الهاتف</label>
                    <input type="tel" id="student-phone">
                </div>
                <div class="form-group">
                    <label for="student-transport">رسوم النقل (درهم)</label>
                    <input type="number" id="student-transport" min="0" step="0.01" value="0">
                </div>
                <div class="form-group">
                    <label for="student-registration">رسوم التسجيل (درهم)</label>
                    <input type="number" id="student-registration" min="0" step="0.01" value="0">
                </div>
                <div style="display: flex; gap: 15px; justify-content: flex-end;">
                    <button type="button" class="btn btn-warning" onclick="closeModal('add-student-modal')">
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-success">
                        ✅ إضافة التلميذ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>إدارة المدفوعات</h3>
                <button class="close-btn" onclick="closeModal('payment-modal')">&times;</button>
            </div>
            <div id="payment-content">
                <!-- سيتم ملء المحتوى هنا -->
            </div>
        </div>
    </div>

    <script>
        // المتغيرات العامة
        let students = [];
        let filteredStudents = [];
        let nextStudentId = 1;

        // الأشهر الدراسية
        const ACADEMIC_MONTHS = [
            "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", "يناير",
            "فبراير", "مارس", "أبريل", "مايو", "يونيو"
        ];

        // تحميل البيانات من localStorage
        function loadData() {
            try {
                const savedStudents = localStorage.getItem('sbea_students');
                if (savedStudents) {
                    students = JSON.parse(savedStudents);
                } else {
                    students = [];
                }

                const savedNextId = localStorage.getItem('sbea_last_student_id');
                if (savedNextId) {
                    nextStudentId = parseInt(savedNextId) + 1;
                } else {
                    nextStudentId = 1;
                }

                filteredStudents = [...students];
                console.log('تم تحميل البيانات بنجاح:', students.length, 'تلميذ');
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                students = [];
                filteredStudents = [];
                nextStudentId = 1;
            }
        }

        // حفظ البيانات في localStorage
        function saveData() {
            try {
                localStorage.setItem('sbea_students', JSON.stringify(students));
                localStorage.setItem('sbea_last_student_id', (nextStudentId - 1).toString());
                console.log('تم حفظ البيانات بنجاح');
                return true;
            } catch (error) {
                console.error('خطأ في حفظ البيانات:', error);
                showAlert('خطأ في حفظ البيانات: ' + error.message, 'error');
                return false;
            }
        }

        // عرض رسائل التنبيه
        function showAlert(message, type = 'success') {
            const alertContainer = document.getElementById('alert-container');
            const alertClass = type === 'error' ? 'alert-error' : 'alert-success';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                    <button onclick="this.parentElement.remove()" style="float: left; background: none; border: none; font-size: 18px; cursor: pointer;">&times;</button>
                </div>
            `;

            // إزالة الرسالة تلقائياً بعد 5 ثوان
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // حساب الإحصائيات
        function calculateStats() {
            let totalFees = 0;
            let totalPaid = 0;
            let totalRemaining = 0;

            students.forEach(student => {
                const monthlyFee = parseFloat(student.monthlyFee || 0);
                const transportFee = parseFloat(student.transportFee || 0);
                const registrationFee = parseFloat(student.registrationFee || 0);

                // إجمالي الرسوم السنوية (10 أشهر + النقل + التسجيل)
                const yearlyFees = (monthlyFee * 10) + transportFee + registrationFee;
                totalFees += yearlyFees;

                // حساب المدفوع
                let studentPaid = 0;
                if (student.payments) {
                    Object.values(student.payments).forEach(payment => {
                        studentPaid += parseFloat(payment.amount || 0);
                    });
                }
                totalPaid += studentPaid;

                // حساب المتبقي
                const studentRemaining = Math.max(0, yearlyFees - studentPaid);
                totalRemaining += studentRemaining;
            });

            // تحديث العرض
            document.getElementById('total-students').textContent = students.length;
            document.getElementById('total-fees').textContent = totalFees.toLocaleString() + ' DH';
            document.getElementById('total-paid').textContent = totalPaid.toLocaleString() + ' DH';
            document.getElementById('total-remaining').textContent = totalRemaining.toLocaleString() + ' DH';
        }

        // عرض التلاميذ في الجدول
        function displayStudents() {
            const tbody = document.getElementById('students-table-body');

            if (filteredStudents.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="no-data">
                            ${students.length === 0 ?
                                'لا توجد بيانات تلاميذ. انقر على "إضافة تلميذ جديد" للبدء.' :
                                'لا توجد نتائج تطابق معايير البحث.'
                            }
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            filteredStudents.forEach(student => {
                const monthlyFee = parseFloat(student.monthlyFee || 0);
                const transportFee = parseFloat(student.transportFee || 0);
                const registrationFee = parseFloat(student.registrationFee || 0);

                // حساب المدفوع والمتبقي
                let studentPaid = 0;
                if (student.payments) {
                    Object.values(student.payments).forEach(payment => {
                        studentPaid += parseFloat(payment.amount || 0);
                    });
                }

                const yearlyFees = (monthlyFee * 10) + transportFee + registrationFee;
                const studentRemaining = Math.max(0, yearlyFees - studentPaid);

                // الحصول على الحرف الأول من الاسم للأفاتار
                const firstLetter = student.name.charAt(0).toUpperCase();

                html += `
                    <tr>
                        <td>
                            <div class="student-avatar">${firstLetter}</div>
                        </td>
                        <td><strong>${student.barcode}</strong></td>
                        <td>${student.name}</td>
                        <td>${student.level}</td>
                        <td>${student.group}</td>
                        <td>${monthlyFee.toLocaleString()} DH</td>
                        <td style="color: #28a745; font-weight: bold;">${studentPaid.toLocaleString()} DH</td>
                        <td style="color: ${studentRemaining > 0 ? '#dc3545' : '#28a745'}; font-weight: bold;">
                            ${studentRemaining.toLocaleString()} DH
                        </td>
                        <td>${student.phone || '-'}</td>
                        <td>
                            <button class="action-btn btn-primary" onclick="openPaymentModal('${student.barcode}')" title="إدارة المدفوعات">
                                💰
                            </button>
                            <button class="action-btn btn-warning" onclick="editStudent('${student.barcode}')" title="تعديل">
                                ✏️
                            </button>
                            <button class="action-btn btn-danger" onclick="deleteStudent('${student.barcode}')" title="حذف">
                                🗑️
                            </button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // تطبيق الفلاتر
        function applyFilters() {
            const nameFilter = document.getElementById('search-name').value.toLowerCase();
            const barcodeFilter = document.getElementById('search-barcode').value;
            const levelFilter = document.getElementById('search-level').value;

            filteredStudents = students.filter(student => {
                const nameMatch = !nameFilter || student.name.toLowerCase().includes(nameFilter);
                const barcodeMatch = !barcodeFilter || student.barcode.includes(barcodeFilter);
                const levelMatch = !levelFilter || student.level === levelFilter;

                return nameMatch && barcodeMatch && levelMatch;
            });

            displayStudents();
            showAlert(`تم العثور على ${filteredStudents.length} نتيجة`, 'success');
        }

        // فتح نافذة إضافة تلميذ
        function openAddStudentModal() {
            document.getElementById('add-student-modal').style.display = 'block';
            // مسح النموذج
            document.getElementById('add-student-form').reset();
        }

        // إغلاق النافذة المنبثقة
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // إضافة تلميذ جديد
        function addStudent(event) {
            event.preventDefault();

            const name = document.getElementById('student-name').value.trim();
            const level = document.getElementById('student-level').value;
            const group = document.getElementById('student-group').value;
            const fee = parseFloat(document.getElementById('student-fee').value) || 0;
            const phone = document.getElementById('student-phone').value.trim();
            const transport = parseFloat(document.getElementById('student-transport').value) || 0;
            const registration = parseFloat(document.getElementById('student-registration').value) || 0;

            if (!name || !level || !group) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const newStudent = {
                id: nextStudentId,
                barcode: nextStudentId.toString(),
                name: name,
                level: level,
                group: group,
                monthlyFee: fee,
                phone: phone,
                transportFee: transport,
                registrationFee: registration,
                payments: {},
                createdAt: new Date().toISOString()
            };

            // إنشاء مدفوعات شهرية فارغة
            ACADEMIC_MONTHS.forEach(month => {
                newStudent.payments[month] = {
                    amount: 0,
                    date: null,
                    status: 'غير مدفوع'
                };
            });

            students.push(newStudent);
            nextStudentId++;

            if (saveData()) {
                filteredStudents = [...students];
                displayStudents();
                calculateStats();
                closeModal('add-student-modal');
                showAlert(`تم إضافة التلميذ ${name} بنجاح (الرقم التسلسلي: ${newStudent.barcode})`, 'success');
            }
        }

        // فتح نافذة المدفوعات
        function openPaymentModal(barcode) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) {
                showAlert('لم يتم العثور على التلميذ', 'error');
                return;
            }

            const modal = document.getElementById('payment-modal');
            const content = document.getElementById('payment-content');

            const monthlyFee = parseFloat(student.monthlyFee || 0);
            const transportFee = parseFloat(student.transportFee || 0);
            const registrationFee = parseFloat(student.registrationFee || 0);

            let totalPaid = 0;
            Object.values(student.payments).forEach(payment => {
                totalPaid += parseFloat(payment.amount || 0);
            });

            const totalFees = (monthlyFee * 10) + transportFee + registrationFee;
            const remaining = Math.max(0, totalFees - totalPaid);

            let paymentsHtml = '';
            ACADEMIC_MONTHS.forEach(month => {
                const payment = student.payments[month] || { amount: 0, status: 'غير مدفوع' };
                const isPaid = parseFloat(payment.amount) >= monthlyFee;
                const statusColor = isPaid ? '#28a745' : '#dc3545';

                paymentsHtml += `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #e9ecef;">
                        <span>${month}</span>
                        <span style="color: ${statusColor}; font-weight: bold;">
                            ${parseFloat(payment.amount).toLocaleString()} DH
                        </span>
                        <button class="btn btn-primary" onclick="payMonth('${barcode}', '${month}', ${monthlyFee})" style="padding: 5px 10px; font-size: 12px;">
                            ${isPaid ? 'مدفوع' : 'دفع'}
                        </button>
                    </div>
                `;
            });

            content.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4>${student.name} - ${student.barcode}</h4>
                    <p><strong>المستوى:</strong> ${student.level} - ${student.group}</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;">
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: #007bff;">${totalFees.toLocaleString()}</div>
                        <div style="color: #666;">إجمالي الرسوم</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">${totalPaid.toLocaleString()}</div>
                        <div style="color: #666;">المدفوع</div>
                    </div>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 1.5rem; font-weight: bold; color: #dc3545;">${remaining.toLocaleString()}</div>
                        <div style="color: #666;">المتبقي</div>
                    </div>
                </div>

                <h5>المدفوعات الشهرية:</h5>
                <div style="max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 8px;">
                    ${paymentsHtml}
                </div>

                <div style="margin-top: 20px; display: flex; gap: 10px; justify-content: center;">
                    <button class="btn btn-success" onclick="payAllMonths('${barcode}', ${monthlyFee})">
                        دفع جميع الأشهر
                    </button>
                    <button class="btn btn-warning" onclick="printReceipt('${barcode}')">
                        طباعة وصل
                    </button>
                </div>
            `;

            modal.style.display = 'block';
        }

        // دفع شهر واحد
        function payMonth(barcode, month, amount) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) return;

            const currentAmount = parseFloat(student.payments[month].amount || 0);
            const newAmount = prompt(`دفع شهر ${month}\nالمبلغ المطلوب: ${amount} درهم\nالمدفوع حالياً: ${currentAmount} درهم\n\nأدخل المبلغ الجديد:`, amount);

            if (newAmount !== null) {
                const paidAmount = parseFloat(newAmount) || 0;
                student.payments[month] = {
                    amount: paidAmount,
                    date: new Date().toISOString(),
                    status: paidAmount >= amount ? 'مدفوع' : (paidAmount > 0 ? 'جزئي' : 'غير مدفوع')
                };

                if (saveData()) {
                    displayStudents();
                    calculateStats();
                    openPaymentModal(barcode); // إعادة فتح النافذة لتحديث البيانات
                    showAlert(`تم تسجيل دفع ${paidAmount} درهم لشهر ${month}`, 'success');
                }
            }
        }

        // دفع جميع الأشهر
        function payAllMonths(barcode, monthlyAmount) {
            if (!confirm('هل تريد تسجيل دفع جميع الأشهر؟')) return;

            const student = students.find(s => s.barcode === barcode);
            if (!student) return;

            ACADEMIC_MONTHS.forEach(month => {
                student.payments[month] = {
                    amount: monthlyAmount,
                    date: new Date().toISOString(),
                    status: 'مدفوع'
                };
            });

            if (saveData()) {
                displayStudents();
                calculateStats();
                openPaymentModal(barcode);
                showAlert(`تم تسجيل دفع جميع الأشهر للتلميذ ${student.name}`, 'success');
            }
        }

        // تعديل تلميذ
        function editStudent(barcode) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) {
                showAlert('لم يتم العثور على التلميذ', 'error');
                return;
            }

            // ملء النموذج بالبيانات الحالية
            document.getElementById('student-name').value = student.name;
            document.getElementById('student-level').value = student.level;
            document.getElementById('student-group').value = student.group;
            document.getElementById('student-fee').value = student.monthlyFee || 0;
            document.getElementById('student-phone').value = student.phone || '';
            document.getElementById('student-transport').value = student.transportFee || 0;
            document.getElementById('student-registration').value = student.registrationFee || 0;

            // تغيير عنوان النافذة
            document.querySelector('#add-student-modal h3').textContent = 'تعديل بيانات التلميذ';
            document.querySelector('#add-student-modal button[type="submit"]').textContent = '✅ حفظ التعديلات';

            // حفظ معرف التلميذ للتعديل
            document.getElementById('add-student-form').dataset.editingBarcode = barcode;

            openAddStudentModal();
        }

        // حذف تلميذ
        function deleteStudent(barcode) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) {
                showAlert('لم يتم العثور على التلميذ', 'error');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف التلميذ: ${student.name}؟\n\nسيتم حذف جميع بياناته ومدفوعاته نهائياً.`)) {
                const index = students.findIndex(s => s.barcode === barcode);
                if (index > -1) {
                    students.splice(index, 1);

                    if (saveData()) {
                        filteredStudents = [...students];
                        displayStudents();
                        calculateStats();
                        showAlert(`تم حذف التلميذ ${student.name} بنجاح`, 'success');
                    }
                }
            }
        }

        // طباعة وصل
        function printReceipt(barcode) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) return;

            const printWindow = window.open('', '_blank');
            const currentDate = new Date().toLocaleDateString('ar-MA');

            let totalPaid = 0;
            Object.values(student.payments).forEach(payment => {
                totalPaid += parseFloat(payment.amount || 0);
            });

            printWindow.document.write(`
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>وصل دفع - ${student.name}</title>
                    <style>
                        body { font-family: Arial, sans-serif; direction: rtl; padding: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .info { margin: 10px 0; }
                        .total { font-size: 18px; font-weight: bold; margin-top: 20px; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>🏫 مؤسسة النور التربوي</h1>
                        <h2>وصل دفع</h2>
                    </div>
                    <div class="info"><strong>اسم التلميذ:</strong> ${student.name}</div>
                    <div class="info"><strong>الرقم التسلسلي:</strong> ${student.barcode}</div>
                    <div class="info"><strong>المستوى:</strong> ${student.level} - ${student.group}</div>
                    <div class="info"><strong>تاريخ الوصل:</strong> ${currentDate}</div>
                    <div class="total">إجمالي المدفوع: ${totalPaid.toLocaleString()} درهم</div>
                    <div style="margin-top: 40px; text-align: center;">
                        <p>شكراً لثقتكم في مؤسسة النور التربوي</p>
                    </div>
                </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();
        }

        // تحديث البيانات
        function refreshData() {
            loadData();
            displayStudents();
            calculateStats();
            showAlert('تم تحديث البيانات بنجاح', 'success');
        }

        // تصدير البيانات
        function exportData() {
            if (students.length === 0) {
                showAlert('لا توجد بيانات للتصدير', 'error');
                return;
            }

            const dataStr = JSON.stringify(students, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `بيانات_التلاميذ_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showAlert('تم تصدير البيانات بنجاح', 'success');
        }

        // إعادة تعيين النظام
        function resetSystem() {
            if (confirm('هل أنت متأكد من إعادة تعيين النظام؟\n\nسيتم حذف جميع البيانات نهائياً!')) {
                if (confirm('تأكيد أخير: سيتم فقدان جميع بيانات التلاميذ والمدفوعات!')) {
                    localStorage.removeItem('sbea_students');
                    localStorage.removeItem('sbea_last_student_id');

                    students = [];
                    filteredStudents = [];
                    nextStudentId = 1;

                    displayStudents();
                    calculateStats();
                    showAlert('تم إعادة تعيين النظام بنجاح', 'success');
                }
            }
        }

        // إعداد معالجات الأحداث
        function setupEventHandlers() {
            // نموذج إضافة التلميذ
            document.getElementById('add-student-form').addEventListener('submit', function(event) {
                event.preventDefault();

                const editingBarcode = this.dataset.editingBarcode;
                if (editingBarcode) {
                    // تعديل تلميذ موجود
                    const student = students.find(s => s.barcode === editingBarcode);
                    if (student) {
                        student.name = document.getElementById('student-name').value.trim();
                        student.level = document.getElementById('student-level').value;
                        student.group = document.getElementById('student-group').value;
                        student.monthlyFee = parseFloat(document.getElementById('student-fee').value) || 0;
                        student.phone = document.getElementById('student-phone').value.trim();
                        student.transportFee = parseFloat(document.getElementById('student-transport').value) || 0;
                        student.registrationFee = parseFloat(document.getElementById('student-registration').value) || 0;

                        if (saveData()) {
                            filteredStudents = [...students];
                            displayStudents();
                            calculateStats();
                            closeModal('add-student-modal');
                            showAlert(`تم تحديث بيانات التلميذ ${student.name} بنجاح`, 'success');
                        }
                    }

                    // إعادة تعيين النموذج
                    delete this.dataset.editingBarcode;
                    document.querySelector('#add-student-modal h3').textContent = 'إضافة تلميذ جديد';
                    document.querySelector('#add-student-modal button[type="submit"]').textContent = '✅ إضافة التلميذ';
                } else {
                    // إضافة تلميذ جديد
                    addStudent(event);
                }
            });

            // البحث التلقائي
            document.getElementById('search-name').addEventListener('input', applyFilters);
            document.getElementById('search-barcode').addEventListener('input', applyFilters);
            document.getElementById('search-level').addEventListener('change', applyFilters);

            // إغلاق النوافذ المنبثقة عند النقر خارجها
            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });
        }

        // تهيئة التطبيق
        function initApp() {
            console.log('🚀 بدء تحميل نظام إدارة التلاميذ...');

            loadData();
            displayStudents();
            calculateStats();
            setupEventHandlers();

            console.log('✅ تم تحميل النظام بنجاح');
            showAlert('مرحباً بك في نظام إدارة التلاميذ المحسن', 'success');
        }

        // تشغيل التطبيق عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initApp);
    </script>
</body>
</html>
// ===================================================================
//                    إدارة الملف الشخصي
// ===================================================================

document.addEventListener('DOMContentLoaded', function() {
    initializeProfile();
});

function initializeProfile() {
    loadUserProfile();
    setupEventListeners();
    loadRecentActivities();
    loadUserSettings();
}

function setupEventListeners() {
    // نموذج تعديل المعلومات
    document.getElementById('profile-form').addEventListener('submit', handleProfileUpdate);
    
    // نموذج تغيير كلمة المرور
    document.getElementById('password-form').addEventListener('submit', handlePasswordChange);
    
    // مراقبة قوة كلمة المرور
    document.getElementById('new-password').addEventListener('input', checkPasswordStrength);
    document.getElementById('confirm-password').addEventListener('input', validatePasswordMatch);
    
    // رفع الصورة
    document.getElementById('avatar-upload').addEventListener('change', handleAvatarUpload);
    
    // إعدادات الحساب
    document.getElementById('notifications-enabled').addEventListener('change', saveUserSettings);
    document.getElementById('auto-sync-enabled').addEventListener('change', saveUserSettings);
    document.getElementById('dark-mode-enabled').addEventListener('change', toggleDarkMode);
    
    // إغلاق النوافذ
    document.querySelectorAll('.close-modal').forEach(btn => {
        btn.addEventListener('click', closeModals);
    });
}

function loadUserProfile() {
    const currentUser = window.authSystem.getCurrentUser();
    if (!currentUser) return;
    
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const user = users.find(u => u.id === currentUser.userId);
    
    if (!user) return;
    
    // تحديث معلومات المستخدم
    document.getElementById('profile-name').textContent = user.name;
    document.getElementById('profile-role').textContent = getRoleName(user.role);
    document.getElementById('profile-last-login').textContent = user.lastLogin ? 
        `آخر تسجيل دخول: ${new Date(user.lastLogin).toLocaleDateString('ar-MA')}` : 
        'لم يسجل دخول من قبل';
    
    // تحديث الصورة
    document.getElementById('user-avatar').src = user.avatar || 'logo';
    
    // تحديث تاريخ الانضمام
    const joinDate = new Date(user.createdAt).toLocaleDateString('ar-MA');
    document.getElementById('profile-join-date').textContent = joinDate;
    
    // حساب مدة العضوية
    const membershipDuration = calculateMembershipDuration(user.createdAt);
    document.getElementById('profile-membership-duration').textContent = membershipDuration;
    
    // تحديث عدد الأنشطة
    const activities = JSON.parse(localStorage.getItem('sbea_user_activities') || '[]');
    const userActivities = activities.filter(a => a.userId === currentUser.userId);
    document.getElementById('profile-activities-count').textContent = userActivities.length;
    
    // ملء نموذج التعديل
    document.getElementById('edit-name').value = user.name;
    document.getElementById('edit-email').value = user.email;
    document.getElementById('edit-phone').value = user.phone;
    document.getElementById('edit-username').value = user.username;
}

function getRoleName(role) {
    const roleNames = {
        'admin': 'مدير',
        'teacher': 'أستاذ',
        'staff': 'موظف'
    };
    return roleNames[role] || role;
}

function calculateMembershipDuration(createdAt) {
    const now = new Date();
    const joinDate = new Date(createdAt);
    const diffTime = Math.abs(now - joinDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 30) {
        return `${diffDays} يوم`;
    } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return `${months} شهر`;
    } else {
        const years = Math.floor(diffDays / 365);
        const remainingMonths = Math.floor((diffDays % 365) / 30);
        return `${years} سنة${remainingMonths > 0 ? ` و ${remainingMonths} شهر` : ''}`;
    }
}

function handleProfileUpdate(event) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('edit-name').value.trim(),
        email: document.getElementById('edit-email').value.trim(),
        phone: document.getElementById('edit-phone').value.trim()
    };
    
    // التحقق من صحة البيانات
    if (!formData.name || !formData.email || !formData.phone) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    if (!isValidEmail(formData.email)) {
        alert('البريد الإلكتروني غير صحيح');
        return;
    }
    
    // تحديث الملف الشخصي
    const result = window.authSystem.updateProfile(formData);
    
    if (result.success) {
        alert('تم تحديث المعلومات بنجاح');
        loadUserProfile();
        
        // تحديث واجهة المستخدم
        window.authSystem.updateUserInterface();
        
        // إشعار المزامنة
        if (window.syncSystem) {
            window.syncSystem.broadcastActivity('profile_update', 'تحديث الملف الشخصي');
        }
    } else {
        alert('خطأ في تحديث المعلومات: ' + result.error);
    }
}

function handlePasswordChange(event) {
    event.preventDefault();
    
    const currentPassword = document.getElementById('current-password').value;
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    
    // التحقق من كلمة المرور الحالية
    const currentUser = window.authSystem.getCurrentUser();
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const user = users.find(u => u.id === currentUser.userId);
    
    if (!user || user.password !== currentPassword) {
        alert('كلمة المرور الحالية غير صحيحة');
        return;
    }
    
    // التحقق من تطابق كلمة المرور الجديدة
    if (newPassword !== confirmPassword) {
        alert('كلمة المرور الجديدة غير متطابقة');
        return;
    }
    
    // التحقق من قوة كلمة المرور
    if (newPassword.length < 6) {
        alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        return;
    }
    
    // تحديث كلمة المرور
    const result = window.authSystem.updatePassword(currentUser.userId, newPassword);
    
    if (result.success) {
        alert('تم تغيير كلمة المرور بنجاح');
        document.getElementById('password-form').reset();
        resetPasswordStrength();
    } else {
        alert('خطأ في تغيير كلمة المرور: ' + result.error);
    }
}

function checkPasswordStrength() {
    const password = document.getElementById('new-password').value;
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');
    
    let strength = 0;
    let strengthLabel = '';
    
    // طول كلمة المرور
    if (password.length >= 6) strength += 1;
    if (password.length >= 8) strength += 1;
    
    // أحرف كبيرة وصغيرة
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength += 1;
    
    // أرقام
    if (/\d/.test(password)) strength += 1;
    
    // رموز خاصة
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;
    
    // تحديد مستوى القوة
    switch (strength) {
        case 0:
        case 1:
            strengthLabel = 'ضعيفة جداً';
            strengthBar.className = 'strength-bar very-weak';
            strengthBar.style.width = '20%';
            break;
        case 2:
            strengthLabel = 'ضعيفة';
            strengthBar.className = 'strength-bar weak';
            strengthBar.style.width = '40%';
            break;
        case 3:
            strengthLabel = 'متوسطة';
            strengthBar.className = 'strength-bar medium';
            strengthBar.style.width = '60%';
            break;
        case 4:
            strengthLabel = 'قوية';
            strengthBar.className = 'strength-bar strong';
            strengthBar.style.width = '80%';
            break;
        case 5:
            strengthLabel = 'قوية جداً';
            strengthBar.className = 'strength-bar very-strong';
            strengthBar.style.width = '100%';
            break;
    }
    
    strengthText.textContent = strengthLabel;
}

function validatePasswordMatch() {
    const newPassword = document.getElementById('new-password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const confirmInput = document.getElementById('confirm-password');
    
    if (confirmPassword && newPassword !== confirmPassword) {
        confirmInput.setCustomValidity('كلمة المرور غير متطابقة');
        confirmInput.style.borderColor = '#dc3545';
    } else {
        confirmInput.setCustomValidity('');
        confirmInput.style.borderColor = '';
    }
}

function resetPasswordStrength() {
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');
    
    strengthBar.className = 'strength-bar';
    strengthBar.style.width = '0%';
    strengthText.textContent = 'قوة كلمة المرور';
}

function loadRecentActivities() {
    const currentUser = window.authSystem.getCurrentUser();
    if (!currentUser) return;
    
    const activities = JSON.parse(localStorage.getItem('sbea_user_activities') || '[]');
    const userActivities = activities
        .filter(a => a.userId === currentUser.userId)
        .slice(0, 10); // آخر 10 أنشطة
    
    const activitiesList = document.getElementById('recent-activities');
    
    if (userActivities.length === 0) {
        activitiesList.innerHTML = '<p class="no-activities">لا توجد أنشطة حديثة</p>';
        return;
    }
    
    let html = '';
    userActivities.forEach(activity => {
        const date = new Date(activity.timestamp).toLocaleDateString('ar-MA');
        const time = new Date(activity.timestamp).toLocaleTimeString('ar-MA');
        
        html += `
            <div class="activity-item">
                <div class="activity-icon ${activity.action}">
                    <i class="fas fa-${getActivityIcon(activity.action)}"></i>
                </div>
                <div class="activity-details">
                    <h6>${activity.description}</h6>
                    <p>${date} - ${time}</p>
                </div>
            </div>
        `;
    });
    
    activitiesList.innerHTML = html;
}

function getActivityIcon(action) {
    const icons = {
        'login': 'sign-in-alt',
        'logout': 'sign-out-alt',
        'create': 'plus',
        'update': 'edit',
        'delete': 'trash',
        'profile_update': 'user-edit'
    };
    return icons[action] || 'info';
}

function loadUserSettings() {
    const settings = JSON.parse(localStorage.getItem('sbea_user_settings') || '{}');
    
    document.getElementById('notifications-enabled').checked = settings.notifications !== false;
    document.getElementById('auto-sync-enabled').checked = settings.autoSync !== false;
    document.getElementById('dark-mode-enabled').checked = settings.darkMode === true;
    
    // تطبيق الوضع المظلم إذا كان مفعلاً
    if (settings.darkMode) {
        document.body.classList.add('dark-mode');
    }
}

function saveUserSettings() {
    const settings = {
        notifications: document.getElementById('notifications-enabled').checked,
        autoSync: document.getElementById('auto-sync-enabled').checked,
        darkMode: document.getElementById('dark-mode-enabled').checked
    };
    
    localStorage.setItem('sbea_user_settings', JSON.stringify(settings));
    
    // تسجيل النشاط
    window.authSystem.logUserActivity('settings_update', 'تحديث إعدادات الحساب');
}

function toggleDarkMode() {
    const isDarkMode = document.getElementById('dark-mode-enabled').checked;
    
    if (isDarkMode) {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }
    
    saveUserSettings();
}

function changeAvatar() {
    document.getElementById('avatar-modal').style.display = 'block';
}

function selectAvatar(avatarPath) {
    const currentUser = window.authSystem.getCurrentUser();
    if (!currentUser) return;
    
    const result = window.authSystem.updateProfile({ avatar: avatarPath });
    
    if (result.success) {
        document.getElementById('user-avatar').src = avatarPath;
        closeModals();
        alert('تم تغيير الصورة بنجاح');
    } else {
        alert('خطأ في تغيير الصورة');
    }
}

function handleAvatarUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح');
        return;
    }
    
    // التحقق من حجم الملف (أقل من 2MB)
    if (file.size > 2 * 1024 * 1024) {
        alert('حجم الصورة يجب أن يكون أقل من 2 ميجابايت');
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        const result = window.authSystem.updateProfile({ avatar: e.target.result });
        
        if (result.success) {
            document.getElementById('user-avatar').src = e.target.result;
            closeModals();
            alert('تم رفع الصورة بنجاح');
        } else {
            alert('خطأ في رفع الصورة');
        }
    };
    
    reader.readAsDataURL(file);
}

function viewAllActivities() {
    // إعادة توجيه لصفحة الأنشطة أو فتح نافذة منبثقة
    window.location.href = 'activities.html';
}

function resetForm() {
    loadUserProfile();
}

function closeModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.style.display = 'none';
    });
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

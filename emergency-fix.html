<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>إصلاح طارئ - نظام النور التربوي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 50px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        h1 { color: #d32f2f; }
        .btn {
            display: inline-block;
            padding: 15px 25px;
            margin: 10px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #333; }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 إصلاح طارئ للنظام</h1>
        
        <div id="status">
            <p><strong>حالة النظام:</strong> <span id="system-status">جاري الفحص...</span></p>
        </div>

        <div id="result" class="result"></div>

        <h3>🔧 إجراءات الإصلاح:</h3>
        
        <button class="btn" onclick="testBasic()">1️⃣ فحص أساسي</button>
        <button class="btn btn-warning" onclick="clearAll()">2️⃣ مسح كامل</button>
        <button class="btn btn-success" onclick="resetSystem()">3️⃣ إعادة تعيين</button>
        
        <hr style="margin: 30px 0;">
        
        <h3>📂 الملفات البديلة:</h3>
        <a href="simple-start.html" class="btn">🏠 بداية بسيطة</a>
        <a href="students-only.html" class="btn">👥 التلاميذ فقط</a>
        
        <hr style="margin: 30px 0;">
        
        <h3>💻 حلول يدوية:</h3>
        <div style="text-align: right; margin: 20px;">
            <p><strong>1. أغلق المتصفح تماماً وأعد فتحه</strong></p>
            <p><strong>2. جرب متصفح آخر (Chrome, Firefox, Edge)</strong></p>
            <p><strong>3. أعد تشغيل الحاسوب</strong></p>
            <p><strong>4. تأكد من أن الملفات في نفس المجلد</strong></p>
        </div>

        <div id="debug-info" style="background: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; font-size: 12px; text-align: left;">
            <strong>معلومات التشخيص:</strong><br>
            <span id="debug-details">جاري التحميل...</span>
        </div>
    </div>

    <script>
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.className = 'result ' + type;
            result.innerHTML = message;
            result.style.display = 'block';
        }

        function testBasic() {
            let issues = [];
            let working = [];

            // فحص JavaScript
            try {
                working.push('JavaScript يعمل');
            } catch(e) {
                issues.push('JavaScript لا يعمل');
            }

            // فحص localStorage
            try {
                localStorage.setItem('test', '1');
                localStorage.removeItem('test');
                working.push('التخزين المحلي يعمل');
            } catch(e) {
                issues.push('التخزين المحلي لا يعمل');
            }

            // فحص البيانات
            try {
                const data = localStorage.getItem('sbea_students');
                if (data) {
                    JSON.parse(data);
                    working.push('بيانات التلاميذ سليمة');
                } else {
                    issues.push('لا توجد بيانات تلاميذ');
                }
            } catch(e) {
                issues.push('بيانات التلاميذ تالفة');
            }

            let message = '';
            if (working.length > 0) {
                message += '<strong>✅ يعمل بشكل صحيح:</strong><br>' + working.join('<br>') + '<br><br>';
            }
            if (issues.length > 0) {
                message += '<strong>❌ مشاكل مكتشفة:</strong><br>' + issues.join('<br>');
            }

            const type = issues.length > 0 ? 'error' : 'success';
            showResult(message, type);

            document.getElementById('system-status').innerHTML = issues.length > 0 ? 
                '<span style="color: red;">يحتاج إصلاح</span>' : 
                '<span style="color: green;">يعمل بشكل جيد</span>';
        }

        function clearAll() {
            if (confirm('هل أنت متأكد؟ سيتم حذف جميع البيانات!')) {
                try {
                    // مسح جميع البيانات
                    Object.keys(localStorage).forEach(key => {
                        if (key.startsWith('sbea_')) {
                            localStorage.removeItem(key);
                        }
                    });
                    
                    showResult('✅ تم مسح جميع البيانات بنجاح!', 'success');
                    
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } catch(e) {
                    showResult('❌ فشل في مسح البيانات: ' + e.message, 'error');
                }
            }
        }

        function resetSystem() {
            try {
                // مسح البيانات القديمة
                Object.keys(localStorage).forEach(key => {
                    if (key.startsWith('sbea_')) {
                        localStorage.removeItem(key);
                    }
                });

                // إنشاء بيانات جديدة
                localStorage.setItem('sbea_students', '[]');
                localStorage.setItem('sbea_teachers', '[]');
                localStorage.setItem('sbea_groups', '[]');
                localStorage.setItem('sbea_last_student_id', '0');

                showResult('✅ تم إعادة تعيين النظام بنجاح!', 'success');
                
                setTimeout(() => {
                    window.location.href = 'simple-start.html';
                }, 2000);
            } catch(e) {
                showResult('❌ فشل في إعادة التعيين: ' + e.message, 'error');
            }
        }

        // تشغيل الفحص تلقائياً
        window.onload = function() {
            // معلومات التشخيص
            const debugInfo = document.getElementById('debug-details');
            debugInfo.innerHTML = 
                'المتصفح: ' + navigator.userAgent.split(' ')[0] + '<br>' +
                'الوقت: ' + new Date().toLocaleString() + '<br>' +
                'الموقع: ' + window.location.href + '<br>' +
                'localStorage متاح: ' + (typeof Storage !== 'undefined' ? 'نعم' : 'لا');

            setTimeout(testBasic, 1000);
        };
    </script>
</body>
</html>

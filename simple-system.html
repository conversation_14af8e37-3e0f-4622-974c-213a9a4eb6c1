<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام بسيط يعمل - مؤسسة النور التربوي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            background: linear-gradient(45deg, #007bff, #28a745);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        .btn-warning { background: #ffc107; color: #333; }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        th {
            background: #007bff;
            color: white;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        tr:hover {
            background: #f5f5f5;
        }
        
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
        }
        .close {
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .student-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #007bff, #28a745);
            color: white;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏫 نظام إدارة مؤسسة النور التربوي - نسخة بسيطة تعمل</h1>
        
        <!-- Alert Area -->
        <div id="alert-area"></div>
        
        <!-- Statistics -->
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-students">0</div>
                <div class="stat-label">إجمالي التلاميذ</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-fees">0</div>
                <div class="stat-label">إجمالي الرسوم (DH)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-paid">0</div>
                <div class="stat-label">المدفوع (DH)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-remaining">0</div>
                <div class="stat-label">المتبقي (DH)</div>
            </div>
        </div>
        
        <!-- Actions -->
        <div style="margin: 20px 0;">
            <button class="btn btn-success" onclick="showAddStudentForm()">➕ إضافة تلميذ جديد</button>
            <button class="btn btn-warning" onclick="refreshData()">🔄 تحديث البيانات</button>
            <button class="btn btn-danger" onclick="clearAllData()">🗑️ مسح جميع البيانات</button>
        </div>
        
        <!-- Students Table -->
        <table id="students-table">
            <thead>
                <tr>
                    <th>الصورة</th>
                    <th>الرقم</th>
                    <th>الاسم</th>
                    <th>المستوى</th>
                    <th>الفوج</th>
                    <th>الرسوم الشهرية</th>
                    <th>المدفوع</th>
                    <th>المتبقي</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody id="students-tbody">
                <tr>
                    <td colspan="9" style="text-align: center; padding: 30px; color: #666;">
                        لا توجد بيانات تلاميذ. انقر على "إضافة تلميذ جديد" للبدء.
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Add Student Modal -->
    <div id="add-student-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>إضافة تلميذ جديد</h3>
            <form id="student-form">
                <div class="form-group">
                    <label>اسم التلميذ *</label>
                    <input type="text" id="student-name" required>
                </div>
                <div class="form-group">
                    <label>المستوى الدراسي *</label>
                    <select id="student-level" required>
                        <option value="">اختر المستوى</option>
                        <option value="الأول ابتدائي">الأول ابتدائي</option>
                        <option value="الثاني ابتدائي">الثاني ابتدائي</option>
                        <option value="الثالث ابتدائي">الثالث ابتدائي</option>
                        <option value="الرابع ابتدائي">الرابع ابتدائي</option>
                        <option value="الخامس ابتدائي">الخامس ابتدائي</option>
                        <option value="السادس ابتدائي">السادس ابتدائي</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>الفوج *</label>
                    <select id="student-group" required>
                        <option value="">اختر الفوج</option>
                        <option value="أ">أ</option>
                        <option value="ب">ب</option>
                        <option value="ج">ج</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>الرسوم الشهرية (درهم)</label>
                    <input type="number" id="student-fee" min="0" value="0">
                </div>
                <div class="form-group">
                    <label>رقم الهاتف</label>
                    <input type="tel" id="student-phone">
                </div>
                <div style="text-align: center; margin-top: 20px;">
                    <button type="submit" class="btn btn-success">✅ إضافة التلميذ</button>
                    <button type="button" class="btn" onclick="closeModal()">إلغاء</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Payment Modal -->
    <div id="payment-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closePaymentModal()">&times;</span>
            <h3>إدارة المدفوعات</h3>
            <div id="payment-content">
                <!-- سيتم ملء المحتوى هنا -->
            </div>
        </div>
    </div>

    <script>
        // المتغيرات العامة
        let students = [];
        let nextId = 1;

        // تحميل البيانات
        function loadData() {
            try {
                const savedStudents = localStorage.getItem('simple_students');
                if (savedStudents) {
                    students = JSON.parse(savedStudents);
                }
                
                const savedNextId = localStorage.getItem('simple_next_id');
                if (savedNextId) {
                    nextId = parseInt(savedNextId);
                }
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                students = [];
                nextId = 1;
            }
        }

        // حفظ البيانات
        function saveData() {
            try {
                localStorage.setItem('simple_students', JSON.stringify(students));
                localStorage.setItem('simple_next_id', nextId.toString());
                return true;
            } catch (error) {
                showAlert('خطأ في حفظ البيانات: ' + error.message, 'error');
                return false;
            }
        }

        // عرض رسالة
        function showAlert(message, type = 'success') {
            const alertArea = document.getElementById('alert-area');
            const alertClass = type === 'error' ? 'alert-error' : 'alert-success';
            alertArea.innerHTML = `<div class="${alertClass}">${message}</div>`;
            setTimeout(() => {
                alertArea.innerHTML = '';
            }, 3000);
        }

        // حساب الإحصائيات
        function updateStats() {
            let totalFees = 0;
            let totalPaid = 0;

            students.forEach(student => {
                const monthlyFee = parseFloat(student.monthlyFee || 0);
                const yearlyFee = monthlyFee * 10; // 10 أشهر
                totalFees += yearlyFee;
                
                const paid = parseFloat(student.totalPaid || 0);
                totalPaid += paid;
            });

            const totalRemaining = totalFees - totalPaid;

            document.getElementById('total-students').textContent = students.length;
            document.getElementById('total-fees').textContent = totalFees.toLocaleString();
            document.getElementById('total-paid').textContent = totalPaid.toLocaleString();
            document.getElementById('total-remaining').textContent = totalRemaining.toLocaleString();
        }

        // عرض التلاميذ
        function displayStudents() {
            const tbody = document.getElementById('students-tbody');
            
            if (students.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" style="text-align: center; padding: 30px; color: #666;">
                            لا توجد بيانات تلاميذ. انقر على "إضافة تلميذ جديد" للبدء.
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            students.forEach(student => {
                const monthlyFee = parseFloat(student.monthlyFee || 0);
                const yearlyFee = monthlyFee * 10;
                const totalPaid = parseFloat(student.totalPaid || 0);
                const remaining = Math.max(0, yearlyFee - totalPaid);
                
                const firstLetter = student.name.charAt(0).toUpperCase();

                html += `
                    <tr>
                        <td><div class="student-avatar">${firstLetter}</div></td>
                        <td><strong>${student.id}</strong></td>
                        <td>${student.name}</td>
                        <td>${student.level}</td>
                        <td>${student.group}</td>
                        <td>${monthlyFee.toLocaleString()} DH</td>
                        <td style="color: #28a745; font-weight: bold;">${totalPaid.toLocaleString()} DH</td>
                        <td style="color: ${remaining > 0 ? '#dc3545' : '#28a745'}; font-weight: bold;">
                            ${remaining.toLocaleString()} DH
                        </td>
                        <td>
                            <button class="btn" onclick="openPayment(${student.id})" title="دفع">💰</button>
                            <button class="btn btn-danger" onclick="deleteStudent(${student.id})" title="حذف">🗑️</button>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = html;
        }

        // إضافة تلميذ
        function addStudent(event) {
            event.preventDefault();

            const name = document.getElementById('student-name').value.trim();
            const level = document.getElementById('student-level').value;
            const group = document.getElementById('student-group').value;
            const fee = parseFloat(document.getElementById('student-fee').value) || 0;
            const phone = document.getElementById('student-phone').value.trim();

            if (!name || !level || !group) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            const newStudent = {
                id: nextId,
                name: name,
                level: level,
                group: group,
                monthlyFee: fee,
                phone: phone,
                totalPaid: 0,
                createdAt: new Date().toISOString()
            };

            students.push(newStudent);
            nextId++;

            if (saveData()) {
                displayStudents();
                updateStats();
                closeModal();
                showAlert(`تم إضافة التلميذ ${name} بنجاح (الرقم: ${newStudent.id})`);
                document.getElementById('student-form').reset();
            }
        }

        // حذف تلميذ
        function deleteStudent(id) {
            const student = students.find(s => s.id === id);
            if (!student) return;

            if (confirm(`هل تريد حذف التلميذ: ${student.name}؟`)) {
                students = students.filter(s => s.id !== id);
                if (saveData()) {
                    displayStudents();
                    updateStats();
                    showAlert(`تم حذف التلميذ ${student.name}`);
                }
            }
        }

        // فتح نافذة الدفع
        function openPayment(id) {
            const student = students.find(s => s.id === id);
            if (!student) return;

            const monthlyFee = parseFloat(student.monthlyFee || 0);
            const yearlyFee = monthlyFee * 10;
            const totalPaid = parseFloat(student.totalPaid || 0);
            const remaining = Math.max(0, yearlyFee - totalPaid);

            document.getElementById('payment-content').innerHTML = `
                <h4>${student.name} - رقم ${student.id}</h4>
                <p><strong>المستوى:</strong> ${student.level} - ${student.group}</p>
                <p><strong>الرسوم السنوية:</strong> ${yearlyFee.toLocaleString()} DH</p>
                <p><strong>المدفوع:</strong> ${totalPaid.toLocaleString()} DH</p>
                <p><strong>المتبقي:</strong> ${remaining.toLocaleString()} DH</p>
                
                <div class="form-group">
                    <label>مبلغ الدفع (درهم)</label>
                    <input type="number" id="payment-amount" min="0" max="${remaining}" value="${remaining}">
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button class="btn btn-success" onclick="processPayment(${id})">✅ تسجيل الدفع</button>
                    <button class="btn" onclick="closePaymentModal()">إلغاء</button>
                </div>
            `;

            document.getElementById('payment-modal').style.display = 'block';
        }

        // معالجة الدفع
        function processPayment(id) {
            const student = students.find(s => s.id === id);
            if (!student) return;

            const amount = parseFloat(document.getElementById('payment-amount').value) || 0;
            if (amount <= 0) {
                showAlert('يرجى إدخال مبلغ صحيح', 'error');
                return;
            }

            student.totalPaid = (parseFloat(student.totalPaid) || 0) + amount;

            if (saveData()) {
                displayStudents();
                updateStats();
                closePaymentModal();
                showAlert(`تم تسجيل دفع ${amount.toLocaleString()} درهم للتلميذ ${student.name}`);
            }
        }

        // فتح نموذج إضافة تلميذ
        function showAddStudentForm() {
            document.getElementById('add-student-modal').style.display = 'block';
        }

        // إغلاق النوافذ
        function closeModal() {
            document.getElementById('add-student-modal').style.display = 'none';
        }

        function closePaymentModal() {
            document.getElementById('payment-modal').style.display = 'none';
        }

        // تحديث البيانات
        function refreshData() {
            loadData();
            displayStudents();
            updateStats();
            showAlert('تم تحديث البيانات');
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل تريد مسح جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء!')) {
                localStorage.removeItem('simple_students');
                localStorage.removeItem('simple_next_id');
                students = [];
                nextId = 1;
                displayStudents();
                updateStats();
                showAlert('تم مسح جميع البيانات');
            }
        }

        // إعداد الأحداث
        document.getElementById('student-form').addEventListener('submit', addStudent);

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const addModal = document.getElementById('add-student-modal');
            const payModal = document.getElementById('payment-modal');
            
            if (event.target === addModal) {
                addModal.style.display = 'none';
            }
            if (event.target === payModal) {
                payModal.style.display = 'none';
            }
        }

        // تهيئة التطبيق
        function init() {
            console.log('تحميل النظام البسيط...');
            loadData();
            displayStudents();
            updateStats();
            console.log('تم تحميل النظام بنجاح');
            showAlert('مرحباً بك في النظام البسيط - يعمل 100%');
        }

        // تشغيل التطبيق
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>

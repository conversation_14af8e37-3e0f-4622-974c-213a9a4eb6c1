// ===================================================================
//                    نظام التشخيص الشامل للتطبيق
// ===================================================================

class SystemDiagnostics {
    constructor() {
        this.diagnostics = [];
        this.systemHealth = 'unknown';
        this.init();
    }

    // تهيئة النظام
    init() {
        this.runDiagnostics();
    }

    // تشغيل جميع فحوصات التشخيص
    runDiagnostics() {
        this.diagnostics = [];
        
        // فحص التخزين المحلي
        this.checkLocalStorage();
        
        // فحص المكتبات الخارجية
        this.checkExternalLibraries();
        
        // فحص سلامة البيانات
        this.checkDataIntegrity();
        
        // فحص الأداء
        this.checkPerformance();
        
        // فحص الأمان
        this.checkSecurity();
        
        // فحص التوافق مع المتصفح
        this.checkBrowserCompatibility();
        
        // فحص الشبكة
        this.checkNetworkStatus();
        
        // تحديد الحالة العامة للنظام
        this.calculateSystemHealth();
    }

    // فحص التخزين المحلي
    checkLocalStorage() {
        try {
            // فحص توفر localStorage
            if (typeof Storage === 'undefined') {
                this.addDiagnostic('error', 'التخزين المحلي', 'التخزين المحلي غير مدعوم في هذا المتصفح');
                return;
            }

            // فحص المساحة المتاحة
            const testKey = 'sbea_storage_test';
            const testData = 'x'.repeat(1024); // 1KB
            
            try {
                localStorage.setItem(testKey, testData);
                localStorage.removeItem(testKey);
                this.addDiagnostic('success', 'التخزين المحلي', 'التخزين المحلي يعمل بشكل صحيح');
            } catch (e) {
                this.addDiagnostic('warning', 'التخزين المحلي', 'مساحة التخزين المحلي ممتلئة أو محدودة');
            }

            // فحص البيانات الأساسية
            const requiredKeys = ['sbea_students', 'sbea_teachers', 'sbea_groups'];
            requiredKeys.forEach(key => {
                const data = localStorage.getItem(key);
                if (!data) {
                    this.addDiagnostic('warning', 'البيانات الأساسية', `البيانات مفقودة: ${key}`);
                } else {
                    try {
                        JSON.parse(data);
                        this.addDiagnostic('success', 'البيانات الأساسية', `البيانات سليمة: ${key}`);
                    } catch (e) {
                        this.addDiagnostic('error', 'البيانات الأساسية', `بيانات تالفة: ${key}`);
                    }
                }
            });

        } catch (error) {
            this.addDiagnostic('error', 'التخزين المحلي', `خطأ في فحص التخزين: ${error.message}`);
        }
    }

    // فحص المكتبات الخارجية
    checkExternalLibraries() {
        const libraries = [
            { name: 'Chart.js', check: () => typeof Chart !== 'undefined', required: false },
            { name: 'XLSX', check: () => typeof XLSX !== 'undefined', required: true },
            { name: 'JsBarcode', check: () => typeof window.JsBarcode !== 'undefined', required: false },
            { name: 'Font Awesome', check: () => document.querySelector('link[href*="font-awesome"]') !== null, required: true }
        ];

        libraries.forEach(lib => {
            try {
                if (lib.check()) {
                    this.addDiagnostic('success', 'المكتبات الخارجية', `${lib.name} محملة بنجاح`);
                } else {
                    const level = lib.required ? 'error' : 'warning';
                    this.addDiagnostic(level, 'المكتبات الخارجية', `${lib.name} غير محملة`);
                }
            } catch (error) {
                this.addDiagnostic('error', 'المكتبات الخارجية', `خطأ في فحص ${lib.name}: ${error.message}`);
            }
        });
    }

    // فحص سلامة البيانات
    checkDataIntegrity() {
        try {
            const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
            const teachers = JSON.parse(localStorage.getItem('sbea_teachers') || '[]');
            const groups = JSON.parse(localStorage.getItem('sbea_groups') || '[]');

            // فحص التلاميذ
            if (Array.isArray(students)) {
                let validStudents = 0;
                let invalidStudents = 0;

                students.forEach((student, index) => {
                    if (student.name && student.barcode && student.level && student.group) {
                        validStudents++;
                    } else {
                        invalidStudents++;
                        this.addDiagnostic('warning', 'سلامة البيانات', `تلميذ بيانات ناقصة في الفهرس ${index}`);
                    }
                });

                this.addDiagnostic('info', 'سلامة البيانات', `التلاميذ: ${validStudents} صحيح، ${invalidStudents} بيانات ناقصة`);
            }

            // فحص الأساتذة
            if (Array.isArray(teachers)) {
                this.addDiagnostic('info', 'سلامة البيانات', `عدد الأساتذة: ${teachers.length}`);
            }

            // فحص المجموعات
            if (Array.isArray(groups)) {
                this.addDiagnostic('info', 'سلامة البيانات', `عدد المجموعات: ${groups.length}`);
            }

            // فحص الأرقام التسلسلية المكررة
            const barcodes = students.map(s => s.barcode).filter(Boolean);
            const uniqueBarcodes = [...new Set(barcodes)];
            
            if (barcodes.length !== uniqueBarcodes.length) {
                this.addDiagnostic('error', 'سلامة البيانات', 'توجد أرقام تسلسلية مكررة للتلاميذ');
            } else {
                this.addDiagnostic('success', 'سلامة البيانات', 'جميع الأرقام التسلسلية فريدة');
            }

        } catch (error) {
            this.addDiagnostic('error', 'سلامة البيانات', `خطأ في فحص البيانات: ${error.message}`);
        }
    }

    // فحص الأداء
    checkPerformance() {
        try {
            // فحص استخدام الذاكرة
            if (performance.memory) {
                const memoryInfo = performance.memory;
                const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024);
                
                this.addDiagnostic('info', 'الأداء', `استخدام الذاكرة: ${usedMB}MB من ${limitMB}MB`);
                
                if (usedMB > limitMB * 0.8) {
                    this.addDiagnostic('warning', 'الأداء', 'استخدام الذاكرة مرتفع');
                }
            }

            // فحص حجم التخزين المحلي
            let totalSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key) && key.startsWith('sbea_')) {
                    totalSize += localStorage[key].length;
                }
            }
            
            const sizeMB = (totalSize / 1024 / 1024).toFixed(2);
            this.addDiagnostic('info', 'الأداء', `حجم البيانات المحلية: ${sizeMB}MB`);
            
            if (sizeMB > 5) {
                this.addDiagnostic('warning', 'الأداء', 'حجم البيانات المحلية كبير، قد يؤثر على الأداء');
            }

        } catch (error) {
            this.addDiagnostic('error', 'الأداء', `خطأ في فحص الأداء: ${error.message}`);
        }
    }

    // فحص الأمان
    checkSecurity() {
        try {
            // فحص HTTPS
            if (location.protocol === 'https:') {
                this.addDiagnostic('success', 'الأمان', 'الموقع يستخدم HTTPS');
            } else if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
                this.addDiagnostic('info', 'الأمان', 'بيئة تطوير محلية');
            } else {
                this.addDiagnostic('warning', 'الأمان', 'الموقع لا يستخدم HTTPS');
            }

            // فحص Content Security Policy
            const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            if (metaCSP) {
                this.addDiagnostic('success', 'الأمان', 'Content Security Policy مفعل');
            } else {
                this.addDiagnostic('info', 'الأمان', 'Content Security Policy غير مفعل');
            }

        } catch (error) {
            this.addDiagnostic('error', 'الأمان', `خطأ في فحص الأمان: ${error.message}`);
        }
    }

    // فحص التوافق مع المتصفح
    checkBrowserCompatibility() {
        try {
            const features = [
                { name: 'localStorage', check: () => typeof Storage !== 'undefined' },
                { name: 'JSON', check: () => typeof JSON !== 'undefined' },
                { name: 'Promise', check: () => typeof Promise !== 'undefined' },
                { name: 'fetch', check: () => typeof fetch !== 'undefined' },
                { name: 'CSS Grid', check: () => CSS.supports('display', 'grid') },
                { name: 'CSS Flexbox', check: () => CSS.supports('display', 'flex') }
            ];

            features.forEach(feature => {
                if (feature.check()) {
                    this.addDiagnostic('success', 'توافق المتصفح', `${feature.name} مدعوم`);
                } else {
                    this.addDiagnostic('error', 'توافق المتصفح', `${feature.name} غير مدعوم`);
                }
            });

            // معلومات المتصفح
            this.addDiagnostic('info', 'معلومات المتصفح', `${navigator.userAgent}`);

        } catch (error) {
            this.addDiagnostic('error', 'توافق المتصفح', `خطأ في فحص التوافق: ${error.message}`);
        }
    }

    // فحص حالة الشبكة
    checkNetworkStatus() {
        try {
            if (navigator.onLine) {
                this.addDiagnostic('success', 'الشبكة', 'متصل بالإنترنت');
            } else {
                this.addDiagnostic('warning', 'الشبكة', 'غير متصل بالإنترنت');
            }

            // فحص سرعة الاتصال
            if (navigator.connection) {
                const connection = navigator.connection;
                this.addDiagnostic('info', 'الشبكة', `نوع الاتصال: ${connection.effectiveType || 'غير محدد'}`);
                
                if (connection.saveData) {
                    this.addDiagnostic('info', 'الشبكة', 'وضع توفير البيانات مفعل');
                }
            }

        } catch (error) {
            this.addDiagnostic('error', 'الشبكة', `خطأ في فحص الشبكة: ${error.message}`);
        }
    }

    // إضافة تشخيص جديد
    addDiagnostic(level, category, message) {
        this.diagnostics.push({
            level: level,
            category: category,
            message: message,
            timestamp: new Date().toISOString()
        });
    }

    // حساب الحالة العامة للنظام
    calculateSystemHealth() {
        const errors = this.diagnostics.filter(d => d.level === 'error').length;
        const warnings = this.diagnostics.filter(d => d.level === 'warning').length;
        const successes = this.diagnostics.filter(d => d.level === 'success').length;

        if (errors > 0) {
            this.systemHealth = 'critical';
        } else if (warnings > 2) {
            this.systemHealth = 'warning';
        } else if (successes > warnings) {
            this.systemHealth = 'good';
        } else {
            this.systemHealth = 'fair';
        }
    }

    // الحصول على تقرير التشخيص
    getDiagnosticsReport() {
        return {
            systemHealth: this.systemHealth,
            diagnostics: this.diagnostics,
            summary: {
                total: this.diagnostics.length,
                errors: this.diagnostics.filter(d => d.level === 'error').length,
                warnings: this.diagnostics.filter(d => d.level === 'warning').length,
                successes: this.diagnostics.filter(d => d.level === 'success').length,
                info: this.diagnostics.filter(d => d.level === 'info').length
            },
            timestamp: new Date().toISOString()
        };
    }

    // تصدير تقرير التشخيص
    exportReport() {
        const report = this.getDiagnosticsReport();
        const dataStr = JSON.stringify(report, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `تقرير_التشخيص_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
    }

    // إصلاح المشاكل التلقائي
    autoFix() {
        let fixedIssues = 0;

        // إصلاح البيانات المفقودة
        if (!localStorage.getItem('sbea_students')) {
            localStorage.setItem('sbea_students', '[]');
            fixedIssues++;
        }

        if (!localStorage.getItem('sbea_teachers')) {
            localStorage.setItem('sbea_teachers', '[]');
            fixedIssues++;
        }

        if (!localStorage.getItem('sbea_groups')) {
            localStorage.setItem('sbea_groups', '[]');
            fixedIssues++;
        }

        // تنظيف البيانات التالفة
        try {
            const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
            const cleanStudents = students.filter(s => s && s.name && s.barcode);
            
            if (cleanStudents.length !== students.length) {
                localStorage.setItem('sbea_students', JSON.stringify(cleanStudents));
                fixedIssues++;
            }
        } catch (error) {
            localStorage.setItem('sbea_students', '[]');
            fixedIssues++;
        }

        return fixedIssues;
    }
}

// إنشاء مثيل النظام
window.systemDiagnostics = new SystemDiagnostics();

// دوال مساعدة
window.runSystemDiagnostics = () => {
    window.systemDiagnostics.runDiagnostics();
    return window.systemDiagnostics.getDiagnosticsReport();
};

window.exportDiagnosticsReport = () => {
    window.systemDiagnostics.exportReport();
};

window.autoFixSystemIssues = () => {
    return window.systemDiagnostics.autoFix();
};

// تصدير النظام
export { SystemDiagnostics };

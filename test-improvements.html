<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحسينات - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .test-section h3 {
            color: #007bff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .demo-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .demo-table {
            margin-top: 20px;
        }
        
        .demo-table table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .demo-table th {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: right;
        }
        
        .demo-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .demo-table tr:hover {
            background: #f8f9fa;
        }
        
        .progress-demo {
            margin: 20px 0;
        }
        
        .counter-demo {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <header>
        <img src="logo" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="profile.html">الملف الشخصي</a></li>
                <li><a href="diagnostics.html">تشخيص النظام</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
                <li><a href="test-improvements.html" class="active">اختبار التحسينات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <div class="test-container">
            <h2><i class="fas fa-flask"></i> اختبار التحسينات الجديدة</h2>
            <p>هذه الصفحة لاختبار جميع التحسينات والميزات الجديدة في النظام</p>

            <!-- اختبار الإشعارات -->
            <div class="test-section auto-fade-in">
                <h3><i class="fas fa-bell"></i> اختبار نظام الإشعارات</h3>
                <div class="test-buttons">
                    <button class="btn btn-success" onclick="testSuccessNotification()">
                        <i class="fas fa-check"></i> إشعار نجاح
                    </button>
                    <button class="btn btn-danger" onclick="testErrorNotification()">
                        <i class="fas fa-times"></i> إشعار خطأ
                    </button>
                    <button class="btn btn-warning" onclick="testWarningNotification()">
                        <i class="fas fa-exclamation-triangle"></i> إشعار تحذير
                    </button>
                    <button class="btn btn-info" onclick="testInfoNotification()">
                        <i class="fas fa-info-circle"></i> إشعار معلومات
                    </button>
                    <button class="btn btn-primary" onclick="testConfirmNotification()">
                        <i class="fas fa-question-circle"></i> إشعار تأكيد
                    </button>
                </div>
            </div>

            <!-- اختبار التحميل -->
            <div class="test-section auto-slide-left">
                <h3><i class="fas fa-spinner"></i> اختبار نظام التحميل</h3>
                <div class="test-buttons">
                    <button class="btn btn-primary" onclick="testGlobalLoading()">
                        <i class="fas fa-globe"></i> تحميل عام
                    </button>
                    <button class="btn btn-info" onclick="testProgressLoading()">
                        <i class="fas fa-tasks"></i> تحميل مع تقدم
                    </button>
                    <button class="btn btn-success" onclick="testButtonLoading(this)">
                        <i class="fas fa-save"></i> تحميل زر
                    </button>
                    <button class="btn btn-warning" onclick="testFormLoading()">
                        <i class="fas fa-form"></i> تحميل نموذج
                    </button>
                </div>
                
                <div class="progress-demo">
                    <h4>شريط التقدم التجريبي:</h4>
                    <div class="progress">
                        <div class="progress-bar" id="demo-progress" style="width: 0%"></div>
                    </div>
                    <button class="btn btn-primary" onclick="animateProgress()">
                        <i class="fas fa-play"></i> تشغيل شريط التقدم
                    </button>
                </div>
            </div>

            <!-- اختبار الرسوم المتحركة -->
            <div class="test-section auto-slide-right">
                <h3><i class="fas fa-magic"></i> اختبار الرسوم المتحركة</h3>
                <div class="test-buttons">
                    <button class="btn btn-primary" onclick="testShakeAnimation()">
                        <i class="fas fa-hand-rock"></i> اهتزاز
                    </button>
                    <button class="btn btn-success" onclick="testPulseAnimation()">
                        <i class="fas fa-heart"></i> نبض
                    </button>
                    <button class="btn btn-info" onclick="testGlowAnimation()">
                        <i class="fas fa-star"></i> توهج
                    </button>
                    <button class="btn btn-warning" onclick="animateCards()">
                        <i class="fas fa-cards-blank"></i> تحريك البطاقات
                    </button>
                </div>

                <div class="demo-cards">
                    <div class="demo-card card-animated">
                        <i class="fas fa-users fa-2x" style="color: #007bff; margin-bottom: 10px;"></i>
                        <h4>بطاقة تجريبية 1</h4>
                        <p>هذه بطاقة لاختبار الرسوم المتحركة</p>
                    </div>
                    <div class="demo-card card-animated">
                        <i class="fas fa-chart-bar fa-2x" style="color: #28a745; margin-bottom: 10px;"></i>
                        <h4>بطاقة تجريبية 2</h4>
                        <p>تحريك البطاقات عند التحميل</p>
                    </div>
                    <div class="demo-card card-animated">
                        <i class="fas fa-cog fa-2x" style="color: #ffc107; margin-bottom: 10px;"></i>
                        <h4>بطاقة تجريبية 3</h4>
                        <p>تأثيرات التحويم والحركة</p>
                    </div>
                </div>
            </div>

            <!-- اختبار التحقق من النماذج -->
            <div class="test-section auto-scale-in">
                <h3><i class="fas fa-check-double"></i> اختبار التحقق من النماذج</h3>
                <form class="demo-form" id="test-form">
                    <div class="form-group">
                        <input type="text" id="test-name" name="test-name" placeholder=" " required>
                        <label for="test-name">الاسم (مطلوب)</label>
                    </div>
                    <div class="form-group">
                        <input type="email" id="test-email" name="test-email" placeholder=" ">
                        <label for="test-email">البريد الإلكتروني</label>
                    </div>
                    <div class="form-group">
                        <input type="tel" id="test-phone" name="test-phone" placeholder=" ">
                        <label for="test-phone">رقم الهاتف</label>
                    </div>
                    <div class="form-group">
                        <input type="number" id="test-number" name="test-number" placeholder=" " min="0">
                        <label for="test-number">رقم موجب</label>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> اختبار التحقق
                        </button>
                    </div>
                </form>
            </div>

            <!-- اختبار العدادات -->
            <div class="test-section auto-bounce-in">
                <h3><i class="fas fa-calculator"></i> اختبار العدادات المتحركة</h3>
                <div class="demo-cards">
                    <div class="demo-card">
                        <div class="counter-demo stat-counter" data-target="150">0</div>
                        <h4>عدد التلاميذ</h4>
                    </div>
                    <div class="demo-card">
                        <div class="counter-demo stat-counter" data-target="25">0</div>
                        <h4>عدد الأساتذة</h4>
                    </div>
                    <div class="demo-card">
                        <div class="counter-demo stat-counter" data-target="8">0</div>
                        <h4>عدد المستويات</h4>
                    </div>
                </div>
                <button class="btn btn-success" onclick="animateCounters()">
                    <i class="fas fa-play"></i> تشغيل العدادات
                </button>
            </div>

            <!-- اختبار الجداول -->
            <div class="test-section auto-fade-in">
                <h3><i class="fas fa-table"></i> اختبار الجداول المتحركة</h3>
                <div class="demo-table">
                    <table>
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>المستوى</th>
                                <th>الفوج</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody id="demo-table-body">
                            <!-- سيتم ملؤها بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>
                <button class="btn btn-info" onclick="animateTable()">
                    <i class="fas fa-table"></i> تحريك الجدول
                </button>
            </div>
        </div>
    </main>

    <footer>
        <p>مؤسسة النور التربوي - نظام إدارة شامل للمؤسسات التعليمية.</p>
    </footer>

    <script src="validation.js"></script>
    <script src="notifications.js"></script>
    <script src="loading.js"></script>
    <script src="animations.js"></script>
    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
    
    <script>
        // اختبار الإشعارات
        function testSuccessNotification() {
            window.showSuccess('تم تنفيذ العملية بنجاح!', 'نجح');
        }
        
        function testErrorNotification() {
            window.showError('حدث خطأ في تنفيذ العملية', 'خطأ');
        }
        
        function testWarningNotification() {
            window.showWarning('تحذير: يرجى التحقق من البيانات', 'تحذير');
        }
        
        function testInfoNotification() {
            window.showInfo('معلومات مفيدة للمستخدم', 'معلومات');
        }
        
        function testConfirmNotification() {
            window.showConfirm(
                'هل أنت متأكد من تنفيذ هذا الإجراء؟',
                () => window.showSuccess('تم التأكيد!'),
                () => window.showInfo('تم الإلغاء')
            );
        }
        
        // اختبار التحميل
        function testGlobalLoading() {
            window.showLoading('جاري التحميل...', 'يرجى الانتظار');
            setTimeout(() => {
                window.hideLoading();
                window.showSuccess('تم التحميل بنجاح!');
            }, 3000);
        }
        
        function testProgressLoading() {
            const id = window.showProgress('جاري المعالجة...', 0);
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += 10;
                window.updateProgress(id, progress, `جاري المعالجة... ${progress}%`);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        window.hideProgress(id);
                        window.showSuccess('تمت المعالجة بنجاح!');
                    }, 500);
                }
            }, 200);
        }
        
        function testButtonLoading(button) {
            window.loadingSystem.loadButton(button, 'جاري الحفظ...');
            setTimeout(() => {
                window.loadingSystem.unloadButton(button);
                window.showSuccess('تم الحفظ بنجاح!');
            }, 2000);
        }
        
        function testFormLoading() {
            const form = document.getElementById('test-form');
            window.loadingSystem.loadForm(form);
            setTimeout(() => {
                window.loadingSystem.unloadForm(form);
                window.showSuccess('تم تحميل النموذج!');
            }, 2000);
        }
        
        // اختبار الرسوم المتحركة
        function testShakeAnimation() {
            const button = event.target;
            window.shake(button);
        }
        
        function testPulseAnimation() {
            const button = event.target;
            window.pulse(button);
        }
        
        function testGlowAnimation() {
            const button = event.target;
            window.glow(button, 2000);
        }
        
        function animateCards() {
            const cards = document.querySelectorAll('.demo-card');
            window.animateGroup(cards, 'bounce-in', 200);
        }
        
        function animateProgress() {
            const progressBar = document.getElementById('demo-progress');
            let width = 0;
            
            const interval = setInterval(() => {
                width += 2;
                progressBar.style.width = width + '%';
                
                if (width >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        progressBar.style.width = '0%';
                    }, 1000);
                }
            }, 50);
        }
        
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-counter');
            counters.forEach(counter => {
                window.animationSystem.animateCounter(counter);
            });
        }
        
        function animateTable() {
            const tableBody = document.getElementById('demo-table-body');
            const sampleData = [
                ['أحمد محمد', 'الأولى ابتدائي', 'فوج أ', 'نشط'],
                ['فاطمة علي', 'الثانية ابتدائي', 'فوج ب', 'نشط'],
                ['محمد حسن', 'الثالثة ابتدائي', 'فوج أ', 'نشط'],
                ['عائشة أحمد', 'الرابعة ابتدائي', 'فوج ب', 'نشط']
            ];
            
            tableBody.innerHTML = '';
            
            sampleData.forEach((rowData, index) => {
                setTimeout(() => {
                    const row = document.createElement('tr');
                    row.className = 'table-row-animated';
                    row.innerHTML = rowData.map(cell => `<td>${cell}</td>`).join('');
                    tableBody.appendChild(row);
                    
                    setTimeout(() => {
                        row.classList.add('visible');
                    }, 50);
                }, index * 200);
            });
        }
        
        // إعداد التحقق من النموذج التجريبي
        document.addEventListener('DOMContentLoaded', function() {
            const testForm = document.getElementById('test-form');
            if (testForm && window.validator) {
                window.validator.setupRealTimeValidation(testForm, {
                    'test-name': ['required', { name: 'minLength', params: [2] }],
                    'test-email': ['email'],
                    'test-phone': ['phone'],
                    'test-number': ['positiveNumber']
                });
                
                testForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    window.showSuccess('تم التحقق من النموذج بنجاح!', 'نجح التحقق');
                });
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <img src="logo" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="profile.html" class="active">الملف الشخصي</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="profile-management">
            <h2><i class="fas fa-user-circle"></i> الملف الشخصي</h2>
            
            <div class="profile-container">
                <!-- معلومات المستخدم -->
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <img id="user-avatar" src="logo" alt="صورة المستخدم">
                            <button class="change-avatar-btn" onclick="changeAvatar()">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="profile-info">
                            <h3 id="profile-name">اسم المستخدم</h3>
                            <p id="profile-role">الدور</p>
                            <p id="profile-last-login">آخر تسجيل دخول</p>
                        </div>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <i class="fas fa-calendar-alt"></i>
                            <div>
                                <span>تاريخ الانضمام</span>
                                <strong id="profile-join-date">-</strong>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <span>مدة العضوية</span>
                                <strong id="profile-membership-duration">-</strong>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-tasks"></i>
                            <div>
                                <span>عدد الأنشطة</span>
                                <strong id="profile-activities-count">0</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تعديل المعلومات -->
                <div class="profile-edit-card">
                    <h4><i class="fas fa-edit"></i> تعديل المعلومات الشخصية</h4>
                    
                    <form id="profile-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="edit-name">الاسم الكامل:</label>
                                <input type="text" id="edit-name" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-email">البريد الإلكتروني:</label>
                                <input type="email" id="edit-email" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-phone">رقم الهاتف:</label>
                                <input type="tel" id="edit-phone" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-username">اسم المستخدم:</label>
                                <input type="text" id="edit-username" readonly>
                                <small>لا يمكن تغيير اسم المستخدم</small>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="primary-btn">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                            <button type="button" class="secondary-btn" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إلغاء
                            </button>
                        </div>
                    </form>
                </div>

                <!-- تغيير كلمة المرور -->
                <div class="profile-password-card">
                    <h4><i class="fas fa-key"></i> تغيير كلمة المرور</h4>
                    
                    <form id="password-form">
                        <div class="form-group">
                            <label for="current-password">كلمة المرور الحالية:</label>
                            <input type="password" id="current-password" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">كلمة المرور الجديدة:</label>
                            <input type="password" id="new-password" required minlength="6">
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">تأكيد كلمة المرور:</label>
                            <input type="password" id="confirm-password" required>
                        </div>
                        
                        <div class="password-strength">
                            <div class="strength-meter">
                                <div class="strength-bar" id="strength-bar"></div>
                            </div>
                            <span id="strength-text">قوة كلمة المرور</span>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="primary-btn">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>

                <!-- أنشطة المستخدم الأخيرة -->
                <div class="profile-activities-card">
                    <h4><i class="fas fa-history"></i> الأنشطة الأخيرة</h4>
                    <div id="recent-activities" class="activities-list">
                        <!-- سيتم ملؤها بواسطة JavaScript -->
                    </div>
                    <button class="view-all-btn" onclick="viewAllActivities()">
                        عرض جميع الأنشطة
                    </button>
                </div>

                <!-- إعدادات الحساب -->
                <div class="profile-settings-card">
                    <h4><i class="fas fa-cog"></i> إعدادات الحساب</h4>
                    
                    <div class="settings-list">
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-bell"></i>
                                <div>
                                    <span>الإشعارات</span>
                                    <small>تلقي إشعارات التحديثات</small>
                                </div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="notifications-enabled" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-sync"></i>
                                <div>
                                    <span>المزامنة التلقائية</span>
                                    <small>مزامنة البيانات تلقائياً</small>
                                </div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="auto-sync-enabled" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <i class="fas fa-moon"></i>
                                <div>
                                    <span>الوضع الليلي</span>
                                    <small>تفعيل الوضع المظلم</small>
                                </div>
                            </div>
                            <label class="switch">
                                <input type="checkbox" id="dark-mode-enabled">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- نافذة تغيير الصورة -->
    <div id="avatar-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-camera"></i> تغيير صورة المستخدم</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="avatar-options">
                    <div class="avatar-option" onclick="selectAvatar('logo')">
                        <img src="logo" alt="الشعار">
                        <span>الشعار</span>
                    </div>
                    <div class="avatar-option" onclick="selectAvatar('default-user.png')">
                        <img src="logo" alt="افتراضي">
                        <span>افتراضي</span>
                    </div>
                </div>
                <div class="upload-section">
                    <input type="file" id="avatar-upload" accept="image/*" style="display: none;">
                    <button onclick="document.getElementById('avatar-upload').click()" class="upload-btn">
                        <i class="fas fa-upload"></i> رفع صورة جديدة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>مؤسسة النور التربوي - نظام إدارة شامل للمؤسسات التعليمية.</p>
    </footer>

    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
    <script src="profile.js"></script>
</body>
</html>

// ===================================================================
//                    إدارة المستخدمين والصلاحيات
// ===================================================================

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من الصلاحيات
    if (!window.authSystem.protectPage(['users_read', 'all'])) {
        return;
    }

    initializeUsersPage();
});

function initializeUsersPage() {
    loadUsers();
    updateUsersStats();
    setupEventListeners();
    loadUserActivities();
}

function setupEventListeners() {
    // أزرار الإدارة
    document.getElementById('add-user-btn').addEventListener('click', openAddUserModal);
    document.getElementById('user-activities-btn').addEventListener('click', openActivitiesModal);
    document.getElementById('export-users-btn').addEventListener('click', exportUsers);
    
    // فلاتر البحث
    document.getElementById('apply-filters-btn').addEventListener('click', applyFilters);
    document.getElementById('clear-filters-btn').addEventListener('click', clearFilters);
    
    // نموذج المستخدم
    document.getElementById('user-form').addEventListener('submit', handleUserSubmit);
    
    // إغلاق النوافذ
    document.querySelectorAll('.close-modal, .cancel-btn').forEach(btn => {
        btn.addEventListener('click', closeModals);
    });
    
    // تحديد الكل
    document.getElementById('select-all-users').addEventListener('change', toggleSelectAll);
    
    // تغيير الدور
    document.getElementById('user-role').addEventListener('change', updatePermissionsByRole);
}

function loadUsers() {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const tbody = document.getElementById('users-table-body');
    
    let html = '';
    users.forEach(user => {
        const lastLogin = user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-MA') : 'لم يسجل دخول';
        const roleNames = {
            'admin': 'مدير',
            'teacher': 'أستاذ', 
            'staff': 'موظف'
        };
        
        html += `
            <tr data-user-id="${user.id}">
                <td><input type="checkbox" class="user-checkbox" value="${user.id}"></td>
                <td><img src="${user.avatar || 'logo'}" alt="صورة ${user.name}" class="user-avatar"></td>
                <td>${user.name}</td>
                <td>${user.username}</td>
                <td><span class="user-role-badge ${user.role}">${roleNames[user.role]}</span></td>
                <td>${user.email}</td>
                <td>${user.phone}</td>
                <td>${lastLogin}</td>
                <td><span class="user-status ${user.isActive ? 'active' : 'inactive'}">${user.isActive ? 'نشط' : 'غير نشط'}</span></td>
                <td>
                    <button onclick="editUser('${user.id}')" class="edit-btn" data-permission="users_update">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="toggleUserStatus('${user.id}')" class="toggle-btn" data-permission="users_update">
                        <i class="fas fa-${user.isActive ? 'ban' : 'check'}"></i>
                    </button>
                    <button onclick="deleteUser('${user.id}')" class="delete-btn" data-permission="users_delete">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button onclick="resetPassword('${user.id}')" class="reset-btn" data-permission="users_update">
                        <i class="fas fa-key"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
    
    // إخفاء الأزرار حسب الصلاحيات
    window.authSystem.updateUserInterface();
}

function updateUsersStats() {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    
    const admins = users.filter(u => u.role === 'admin').length;
    const teachers = users.filter(u => u.role === 'teacher').length;
    const staff = users.filter(u => u.role === 'staff').length;
    const online = users.filter(u => u.lastLogin && isRecentLogin(u.lastLogin)).length;
    
    document.getElementById('admins-count').textContent = admins;
    document.getElementById('teachers-count').textContent = teachers;
    document.getElementById('staff-count').textContent = staff;
    document.getElementById('online-count').textContent = online;
}

function isRecentLogin(lastLogin) {
    const loginTime = new Date(lastLogin);
    const now = new Date();
    const diffHours = (now - loginTime) / (1000 * 60 * 60);
    return diffHours < 24; // اعتبار المستخدم متصل إذا سجل دخول خلال 24 ساعة
}

function openAddUserModal() {
    document.getElementById('user-modal-title').innerHTML = '<i class="fas fa-user-plus"></i> إضافة مستخدم جديد';
    document.getElementById('user-form').reset();
    document.getElementById('user-form').removeAttribute('data-user-id');
    setupPermissionsGrid();
    document.getElementById('user-modal').style.display = 'block';
}

function editUser(userId) {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const user = users.find(u => u.id === userId);
    
    if (!user) return;
    
    document.getElementById('user-modal-title').innerHTML = '<i class="fas fa-user-edit"></i> تعديل المستخدم';
    document.getElementById('user-name').value = user.name;
    document.getElementById('user-username').value = user.username;
    document.getElementById('user-email').value = user.email;
    document.getElementById('user-phone').value = user.phone;
    document.getElementById('user-role').value = user.role;
    document.getElementById('user-password').value = '';
    document.getElementById('user-password').placeholder = 'اتركه فارغاً للاحتفاظ بكلمة المرور الحالية';
    document.getElementById('user-active').checked = user.isActive;
    
    document.getElementById('user-form').setAttribute('data-user-id', userId);
    setupPermissionsGrid(user.permissions);
    document.getElementById('user-modal').style.display = 'block';
}

function setupPermissionsGrid(userPermissions = []) {
    const grid = document.getElementById('permissions-grid');
    const permissions = window.authSystem.AVAILABLE_PERMISSIONS;
    
    let html = '';
    Object.entries(permissions).forEach(([key, description]) => {
        const checked = userPermissions.includes(key) ? 'checked' : '';
        html += `
            <div class="permission-item">
                <input type="checkbox" id="perm-${key}" value="${key}" ${checked}>
                <label for="perm-${key}">${description}</label>
            </div>
        `;
    });
    
    grid.innerHTML = html;
}

function updatePermissionsByRole() {
    const role = document.getElementById('user-role').value;
    const rolePermissions = window.authSystem.ROLE_PERMISSIONS[role] || [];
    
    // إلغاء تحديد جميع الصلاحيات
    document.querySelectorAll('#permissions-grid input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // تحديد صلاحيات الدور
    rolePermissions.forEach(permission => {
        const checkbox = document.getElementById(`perm-${permission}`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });
}

function handleUserSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const userId = event.target.getAttribute('data-user-id');
    const isEdit = !!userId;
    
    // جمع الصلاحيات المحددة
    const permissions = [];
    document.querySelectorAll('#permissions-grid input[type="checkbox"]:checked').forEach(checkbox => {
        permissions.push(checkbox.value);
    });
    
    const userData = {
        id: userId || 'user_' + Date.now(),
        name: document.getElementById('user-name').value.trim(),
        username: document.getElementById('user-username').value.trim(),
        email: document.getElementById('user-email').value.trim(),
        phone: document.getElementById('user-phone').value.trim(),
        role: document.getElementById('user-role').value,
        permissions: permissions,
        isActive: document.getElementById('user-active').checked,
        avatar: 'logo'
    };
    
    // كلمة المرور
    const password = document.getElementById('user-password').value;
    if (password || !isEdit) {
        userData.password = password || 'password123';
    }
    
    // التواريخ
    if (!isEdit) {
        userData.createdAt = new Date().toISOString();
        userData.lastLogin = null;
    }
    
    // حفظ المستخدم
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    
    if (isEdit) {
        const index = users.findIndex(u => u.id === userId);
        if (index !== -1) {
            users[index] = { ...users[index], ...userData };
        }
    } else {
        // التحقق من عدم تكرار اسم المستخدم
        if (users.some(u => u.username === userData.username)) {
            alert('اسم المستخدم موجود بالفعل');
            return;
        }
        users.push(userData);
    }
    
    localStorage.setItem('sbea_users', JSON.stringify(users));
    
    // تسجيل النشاط
    window.authSystem.logUserActivity(
        isEdit ? 'update' : 'create',
        `${isEdit ? 'تعديل' : 'إضافة'} المستخدم: ${userData.name}`
    );
    
    // إشعار المزامنة
    if (window.syncSystem) {
        window.syncSystem.broadcastUpdate('sbea_users', users, isEdit ? 'update' : 'create');
    }
    
    // تحديث الواجهة
    loadUsers();
    updateUsersStats();
    closeModals();
    
    alert(`تم ${isEdit ? 'تعديل' : 'إضافة'} المستخدم بنجاح`);
}

function deleteUser(userId) {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const user = users.find(u => u.id === userId);
    
    if (!user) return;
    
    if (confirm(`هل أنت متأكد من حذف المستخدم "${user.name}"؟`)) {
        const updatedUsers = users.filter(u => u.id !== userId);
        localStorage.setItem('sbea_users', JSON.stringify(updatedUsers));
        
        // تسجيل النشاط
        window.authSystem.logUserActivity('delete', `حذف المستخدم: ${user.name}`);
        
        // إشعار المزامنة
        if (window.syncSystem) {
            window.syncSystem.broadcastUpdate('sbea_users', updatedUsers, 'delete');
        }
        
        loadUsers();
        updateUsersStats();
        
        alert('تم حذف المستخدم بنجاح');
    }
}

function toggleUserStatus(userId) {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) return;
    
    users[userIndex].isActive = !users[userIndex].isActive;
    localStorage.setItem('sbea_users', JSON.stringify(users));
    
    // تسجيل النشاط
    window.authSystem.logUserActivity(
        'update',
        `${users[userIndex].isActive ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم: ${users[userIndex].name}`
    );
    
    // إشعار المزامنة
    if (window.syncSystem) {
        window.syncSystem.broadcastUpdate('sbea_users', users, 'update');
    }
    
    loadUsers();
    updateUsersStats();
}

function resetPassword(userId) {
    const newPassword = prompt('أدخل كلمة المرور الجديدة:');
    if (!newPassword) return;
    
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) return;
    
    users[userIndex].password = newPassword;
    localStorage.setItem('sbea_users', JSON.stringify(users));
    
    // تسجيل النشاط
    window.authSystem.logUserActivity('update', `إعادة تعيين كلمة مرور المستخدم: ${users[userIndex].name}`);
    
    alert('تم إعادة تعيين كلمة المرور بنجاح');
}

function applyFilters() {
    const roleFilter = document.getElementById('role-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const searchTerm = document.getElementById('search-users').value.toLowerCase();
    
    const rows = document.querySelectorAll('#users-table-body tr');
    
    rows.forEach(row => {
        const userId = row.getAttribute('data-user-id');
        const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
        const user = users.find(u => u.id === userId);
        
        if (!user) return;
        
        let show = true;
        
        // فلتر الدور
        if (roleFilter && user.role !== roleFilter) {
            show = false;
        }
        
        // فلتر الحالة
        if (statusFilter) {
            const isActive = statusFilter === 'active';
            if (user.isActive !== isActive) {
                show = false;
            }
        }
        
        // فلتر البحث
        if (searchTerm) {
            const searchableText = `${user.name} ${user.username} ${user.email}`.toLowerCase();
            if (!searchableText.includes(searchTerm)) {
                show = false;
            }
        }
        
        row.style.display = show ? '' : 'none';
    });
}

function clearFilters() {
    document.getElementById('role-filter').value = '';
    document.getElementById('status-filter').value = '';
    document.getElementById('search-users').value = '';
    
    document.querySelectorAll('#users-table-body tr').forEach(row => {
        row.style.display = '';
    });
}

function toggleSelectAll() {
    const selectAll = document.getElementById('select-all-users');
    const checkboxes = document.querySelectorAll('.user-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function exportUsers() {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    
    const exportData = users.map(user => ({
        'الاسم': user.name,
        'اسم المستخدم': user.username,
        'البريد الإلكتروني': user.email,
        'الهاتف': user.phone,
        'الدور': user.role,
        'الحالة': user.isActive ? 'نشط' : 'غير نشط',
        'تاريخ الإنشاء': new Date(user.createdAt).toLocaleDateString('ar-MA'),
        'آخر تسجيل دخول': user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-MA') : 'لم يسجل دخول'
    }));
    
    // تصدير كـ CSV
    const csv = convertToCSV(exportData);
    downloadCSV(csv, `المستخدمين_${new Date().toISOString().split('T')[0]}.csv`);
}

function openActivitiesModal() {
    loadUserActivities();
    document.getElementById('activities-modal').style.display = 'block';
}

function loadUserActivities() {
    const activities = JSON.parse(localStorage.getItem('sbea_user_activities') || '[]');
    const activitiesList = document.getElementById('activities-list');
    
    let html = '<div class="activities-table">';
    
    activities.slice(0, 100).forEach(activity => { // عرض آخر 100 نشاط
        const date = new Date(activity.timestamp).toLocaleDateString('ar-MA');
        const time = new Date(activity.timestamp).toLocaleTimeString('ar-MA');
        
        html += `
            <div class="activity-item">
                <div class="activity-icon ${activity.action}">
                    <i class="fas fa-${getActivityIcon(activity.action)}"></i>
                </div>
                <div class="activity-details">
                    <h6>${activity.description}</h6>
                    <p>المستخدم: ${activity.name || activity.username}</p>
                </div>
                <div class="activity-time">
                    <div>${date}</div>
                    <div>${time}</div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    activitiesList.innerHTML = html;
}

function getActivityIcon(action) {
    const icons = {
        'login': 'sign-in-alt',
        'logout': 'sign-out-alt',
        'create': 'plus',
        'update': 'edit',
        'delete': 'trash'
    };
    return icons[action] || 'info';
}

function closeModals() {
    document.querySelectorAll('.modal').forEach(modal => {
        modal.style.display = 'none';
    });
}

// دوال مساعدة
function convertToCSV(data) {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    return csvContent;
}

function downloadCSV(csv, filename) {
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    link.click();
}

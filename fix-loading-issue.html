<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة التحميل - نظام النور التربوي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .fix-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
        }

        .problem {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }

        .solution {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #155724;
            text-align: right;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .steps {
            text-align: right;
            margin: 20px 0;
        }

        .steps ol {
            padding-right: 20px;
        }

        .steps li {
            margin: 10px 0;
            line-height: 1.6;
        }

        .diagnostic-result {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .status-ok {
            color: #28a745;
            font-weight: bold;
        }

        .status-error {
            color: #dc3545;
            font-weight: bold;
        }

        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="icon">🔧</div>
        <h1>إصلاح مشكلة التحميل</h1>
        
        <div class="problem">
            <h3>🚨 المشكلة المكتشفة:</h3>
            <p>الصفحة تستمر في التحميل ولا تظهر المحتوى</p>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري فحص النظام...</p>
        </div>

        <div class="diagnostic-result" id="diagnostic-result" style="display: none;">
            <h3>📊 نتائج التشخيص:</h3>
            <div id="diagnostic-details"></div>
        </div>

        <div class="solution">
            <h3>✅ الحلول المقترحة:</h3>
            <div class="steps">
                <ol>
                    <li><strong>إعادة تحميل قوية:</strong> اضغط Ctrl + F5</li>
                    <li><strong>مسح الذاكرة المؤقتة:</strong> Ctrl + Shift + Delete</li>
                    <li><strong>فحص الأخطاء:</strong> اضغط F12 وانظر للأخطاء</li>
                    <li><strong>تجربة متصفح آخر:</strong> Chrome أو Firefox</li>
                    <li><strong>إعادة تشغيل المتصفح:</strong> أغلق وافتح المتصفح</li>
                </ol>
            </div>
        </div>

        <div style="margin: 30px 0;">
            <button class="btn btn-primary" onclick="runDiagnostic()">🔍 تشخيص المشكلة</button>
            <button class="btn btn-success" onclick="fixIssues()">🛠️ إصلاح تلقائي</button>
            <button class="btn btn-warning" onclick="clearCache()">🗑️ مسح الذاكرة</button>
            <a href="index.html" class="btn btn-primary">🔄 إعادة المحاولة</a>
        </div>

        <div style="margin: 20px 0;">
            <a href="quick-test.html" class="btn btn-warning">🧪 اختبار سريع</a>
            <a href="teacher-students.html" class="btn btn-success">👨‍🏫 واجهة الأساتذة</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 2px solid #e0e0e0;">
            <h4>📞 إذا استمرت المشكلة:</h4>
            <p style="color: #666; margin: 10px 0;">
                1. تأكد من تمكين JavaScript في المتصفح<br>
                2. تحقق من اتصال الإنترنت<br>
                3. جرب فتح الملفات من مجلد آخر<br>
                4. استخدم خادم ويب محلي
            </p>
        </div>
    </div>

    <script>
        function runDiagnostic() {
            const loading = document.getElementById('loading');
            const result = document.getElementById('diagnostic-result');
            const details = document.getElementById('diagnostic-details');
            
            loading.style.display = 'block';
            result.style.display = 'none';
            
            setTimeout(() => {
                loading.style.display = 'none';
                result.style.display = 'block';
                
                let diagnosticHtml = '';
                let issues = [];
                
                // فحص JavaScript
                try {
                    eval('1+1');
                    diagnosticHtml += '<div class="status-ok">✅ JavaScript: يعمل بشكل صحيح</div>';
                } catch (e) {
                    diagnosticHtml += '<div class="status-error">❌ JavaScript: معطل أو لا يعمل</div>';
                    issues.push('JavaScript معطل');
                }
                
                // فحص localStorage
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    diagnosticHtml += '<div class="status-ok">✅ التخزين المحلي: يعمل بشكل صحيح</div>';
                } catch (e) {
                    diagnosticHtml += '<div class="status-error">❌ التخزين المحلي: لا يعمل</div>';
                    issues.push('التخزين المحلي معطل');
                }
                
                // فحص الاتصال
                if (navigator.onLine) {
                    diagnosticHtml += '<div class="status-ok">✅ الاتصال: متصل بالإنترنت</div>';
                } else {
                    diagnosticHtml += '<div class="status-warning">⚠️ الاتصال: غير متصل بالإنترنت</div>';
                }
                
                // فحص المتصفح
                const isChrome = /Chrome/.test(navigator.userAgent);
                const isFirefox = /Firefox/.test(navigator.userAgent);
                const isEdge = /Edge/.test(navigator.userAgent);
                
                if (isChrome || isFirefox || isEdge) {
                    diagnosticHtml += '<div class="status-ok">✅ المتصفح: متوافق</div>';
                } else {
                    diagnosticHtml += '<div class="status-warning">⚠️ المتصفح: قد لا يكون متوافق تماماً</div>';
                }
                
                // فحص حجم البيانات
                let dataSize = 0;
                try {
                    for (let key in localStorage) {
                        if (key.startsWith('sbea_')) {
                            dataSize += localStorage[key].length;
                        }
                    }
                    const sizeMB = (dataSize / 1024 / 1024).toFixed(2);
                    
                    if (sizeMB < 5) {
                        diagnosticHtml += `<div class="status-ok">✅ حجم البيانات: ${sizeMB}MB (طبيعي)</div>`;
                    } else {
                        diagnosticHtml += `<div class="status-warning">⚠️ حجم البيانات: ${sizeMB}MB (كبير)</div>`;
                        issues.push('حجم البيانات كبير');
                    }
                } catch (e) {
                    diagnosticHtml += '<div class="status-error">❌ لا يمكن فحص حجم البيانات</div>';
                }
                
                details.innerHTML = diagnosticHtml;
                
                if (issues.length > 0) {
                    details.innerHTML += '<div style="margin-top: 15px; color: #dc3545;"><strong>المشاكل المكتشفة:</strong><br>' + issues.join('<br>') + '</div>';
                }
            }, 2000);
        }
        
        function fixIssues() {
            if (confirm('هل تريد تشغيل الإصلاح التلقائي؟ سيتم مسح بعض البيانات المؤقتة.')) {
                try {
                    // مسح البيانات التالفة
                    const keysToCheck = Object.keys(localStorage).filter(key => key.startsWith('sbea_'));
                    let fixed = 0;
                    
                    keysToCheck.forEach(key => {
                        try {
                            JSON.parse(localStorage.getItem(key));
                        } catch (e) {
                            localStorage.removeItem(key);
                            fixed++;
                        }
                    });
                    
                    // إنشاء البيانات الأساسية إذا لم تكن موجودة
                    if (!localStorage.getItem('sbea_students')) {
                        localStorage.setItem('sbea_students', '[]');
                        fixed++;
                    }
                    
                    if (!localStorage.getItem('sbea_teachers')) {
                        localStorage.setItem('sbea_teachers', '[]');
                        fixed++;
                    }
                    
                    alert(`تم إصلاح ${fixed} مشكلة. جاري إعادة تحميل الصفحة...`);
                    
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                    
                } catch (error) {
                    alert('حدث خطأ أثناء الإصلاح: ' + error.message);
                }
            }
        }
        
        function clearCache() {
            if (confirm('هل تريد مسح جميع البيانات المحفوظة؟ سيتم فقدان جميع البيانات!')) {
                try {
                    // مسح جميع البيانات المتعلقة بالنظام
                    const keysToRemove = Object.keys(localStorage).filter(key => key.startsWith('sbea_'));
                    keysToRemove.forEach(key => localStorage.removeItem(key));
                    
                    alert('تم مسح جميع البيانات. جاري إعادة تحميل الصفحة...');
                    
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                    
                } catch (error) {
                    alert('حدث خطأ أثناء مسح البيانات: ' + error.message);
                }
            }
        }
        
        // تشغيل التشخيص تلقائياً عند التحميل
        window.addEventListener('load', () => {
            setTimeout(runDiagnostic, 1000);
        });
    </script>
</body>
</html>

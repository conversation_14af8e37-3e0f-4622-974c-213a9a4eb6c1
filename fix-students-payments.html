<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل التلاميذ والمدفوعات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #dc3545;
        }
        .problem-section {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #721c24;
        }
        .solution-section {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #155724;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            margin: 10px 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid transparent;
        }
        .card.error { border-color: #dc3545; background: #f8d7da; }
        .card.success { border-color: #28a745; background: #d4edda; }
        .card.warning { border-color: #ffc107; background: #fff3cd; }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .result.show { display: block; }
        .loading {
            text-align: center;
            margin: 20px 0;
            display: none;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح مشاكل التلاميذ والمدفوعات</h1>
            <p>حل مشاكل صفحة إدارة التلاميذ وحساب المدفوعات</p>
        </div>

        <div class="problem-section">
            <h3>🚨 المشاكل المكتشفة:</h3>
            <ul>
                <li>صفحة إدارة التلاميذ تبقى تدور ولا تحمل</li>
                <li>الدفع السريع لا يستجيب</li>
                <li>إجمالي الباقي يظهر صفر بدلاً من المبلغ الكامل</li>
                <li>عدم حساب الرسوم الإضافية (النقل، التسجيل، الكتب)</li>
            </ul>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري فحص وإصلاح المشاكل...</p>
        </div>

        <div class="stats" id="current-stats">
            <div class="stat-item">
                <span class="stat-number" id="students-count">0</span>
                <span class="stat-label">التلاميذ</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="total-fees">0</span>
                <span class="stat-label">إجمالي الرسوم</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="total-paid">0</span>
                <span class="stat-label">إجمالي المدفوع</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="total-remaining">0</span>
                <span class="stat-label">إجمالي الباقي</span>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h4>🔍 تشخيص المشاكل</h4>
                <p>فحص شامل لمشاكل النظام</p>
                <button class="btn btn-primary" onclick="diagnoseProblem()">تشخيص</button>
            </div>

            <div class="card">
                <h4>🛠️ إصلاح حساب المدفوعات</h4>
                <p>إصلاح حساب المبالغ الباقية والرسوم</p>
                <button class="btn btn-warning" onclick="fixPaymentCalculations()">إصلاح</button>
            </div>

            <div class="card">
                <h4>🔄 إعادة بناء البيانات</h4>
                <p>إعادة بناء بيانات المدفوعات من الصفر</p>
                <button class="btn btn-danger" onclick="rebuildPaymentData()">إعادة بناء</button>
            </div>

            <div class="card">
                <h4>📊 تحديث الإحصائيات</h4>
                <p>إعادة حساب جميع الإحصائيات</p>
                <button class="btn btn-success" onclick="updateAllStats()">تحديث</button>
            </div>
        </div>

        <div class="result" id="result"></div>

        <div class="solution-section">
            <h3>✅ الحلول المطبقة:</h3>
            <div id="applied-fixes"></div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="students.html" class="btn btn-primary">🔄 اختبار صفحة التلاميذ</a>
            <a href="financial.html" class="btn btn-success">💰 اختبار النظام المالي</a>
            <a href="simple-start.html" class="btn btn-warning">🏠 العودة للبداية</a>
        </div>
    </div>

    <script>
        // الأشهر الدراسية
        const ACADEMIC_MONTHS = [
            "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", "يناير",
            "فبراير", "مارس", "أبريل", "مايو", "يونيو"
        ];

        function showResult(message, type) {
            const result = document.getElementById('result');
            result.className = 'result show ' + type;
            result.innerHTML = message;
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        function updateStats() {
            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                
                let totalFees = 0;
                let totalPaid = 0;
                let totalRemaining = 0;

                students.forEach(student => {
                    const monthlyFee = parseFloat(student.monthlyFee) || 0;
                    const transportFee = parseFloat(student.transportFee) || 0;
                    const registrationFee = parseFloat(student.registrationFee) || 0;
                    
                    // حساب إجمالي الرسوم السنوية
                    const yearlyFees = (monthlyFee * 10) + transportFee + registrationFee;
                    totalFees += yearlyFees;

                    // حساب المدفوع
                    let studentPaid = 0;
                    if (student.monthlyPayments) {
                        Object.values(student.monthlyPayments).forEach(payment => {
                            studentPaid += parseFloat(payment.amount) || 0;
                        });
                    }
                    totalPaid += studentPaid;

                    // حساب الباقي
                    const studentRemaining = Math.max(0, yearlyFees - studentPaid);
                    totalRemaining += studentRemaining;
                });

                document.getElementById('students-count').textContent = students.length;
                document.getElementById('total-fees').textContent = totalFees.toLocaleString() + ' DH';
                document.getElementById('total-paid').textContent = totalPaid.toLocaleString() + ' DH';
                document.getElementById('total-remaining').textContent = totalRemaining.toLocaleString() + ' DH';

            } catch (error) {
                console.error('خطأ في تحديث الإحصائيات:', error);
            }
        }

        function diagnoseProblem() {
            showLoading(true);
            
            setTimeout(() => {
                showLoading(false);
                
                let issues = [];
                let fixes = [];

                try {
                    const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                    
                    if (students.length === 0) {
                        issues.push('لا توجد بيانات تلاميذ');
                    } else {
                        // فحص بنية البيانات
                        let studentsWithoutPayments = 0;
                        let studentsWithWrongCalculations = 0;

                        students.forEach(student => {
                            if (!student.monthlyPayments) {
                                studentsWithoutPayments++;
                            } else {
                                // فحص حساب المبالغ
                                const monthlyFee = parseFloat(student.monthlyFee) || 0;
                                let totalPaid = 0;
                                let calculatedRemaining = 0;

                                Object.values(student.monthlyPayments).forEach(payment => {
                                    totalPaid += parseFloat(payment.amount) || 0;
                                    calculatedRemaining += parseFloat(payment.remaining) || 0;
                                });

                                const expectedTotal = monthlyFee * 10;
                                const expectedRemaining = Math.max(0, expectedTotal - totalPaid);

                                if (Math.abs(calculatedRemaining - expectedRemaining) > 1) {
                                    studentsWithWrongCalculations++;
                                }
                            }
                        });

                        if (studentsWithoutPayments > 0) {
                            issues.push(`${studentsWithoutPayments} تلميذ بدون بيانات مدفوعات`);
                        }

                        if (studentsWithWrongCalculations > 0) {
                            issues.push(`${studentsWithWrongCalculations} تلميذ بحسابات خاطئة`);
                        }
                    }

                    // فحص الدوال
                    if (typeof window.openPaymentsModal === 'undefined') {
                        issues.push('دالة الدفع السريع مفقودة');
                    }

                } catch (error) {
                    issues.push('بيانات تالفة: ' + error.message);
                }

                let message = '<h4>نتائج التشخيص:</h4>';
                if (issues.length > 0) {
                    message += '<strong>❌ مشاكل مكتشفة:</strong><ul>';
                    issues.forEach(issue => message += `<li>${issue}</li>`);
                    message += '</ul>';
                } else {
                    message += '<strong>✅ لم يتم اكتشاف مشاكل</strong>';
                }

                showResult(message, issues.length > 0 ? 'error' : 'success');
            }, 2000);
        }

        function fixPaymentCalculations() {
            if (!confirm('هل تريد إصلاح حسابات المدفوعات؟ سيتم إعادة حساب جميع المبالغ.')) {
                return;
            }

            showLoading(true);

            setTimeout(() => {
                try {
                    const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                    let fixedCount = 0;

                    students.forEach(student => {
                        const monthlyFee = parseFloat(student.monthlyFee) || 0;
                        const transportFee = parseFloat(student.transportFee) || 0;
                        const registrationFee = parseFloat(student.registrationFee) || 0;

                        // إنشاء بيانات المدفوعات إذا لم تكن موجودة
                        if (!student.monthlyPayments) {
                            student.monthlyPayments = {};
                            fixedCount++;
                        }

                        // إنشاء مدفوعات للأشهر المفقودة
                        ACADEMIC_MONTHS.forEach(month => {
                            if (!student.monthlyPayments[month]) {
                                student.monthlyPayments[month] = {
                                    status: 'غير مدفوع',
                                    amount: 0,
                                    remaining: monthlyFee,
                                    dueAmount: monthlyFee
                                };
                                fixedCount++;
                            } else {
                                // إصلاح الحسابات الموجودة
                                const payment = student.monthlyPayments[month];
                                const paidAmount = parseFloat(payment.amount) || 0;
                                const dueAmount = parseFloat(payment.dueAmount) || monthlyFee;
                                
                                payment.remaining = Math.max(0, dueAmount - paidAmount);
                                payment.dueAmount = monthlyFee;
                                
                                if (payment.remaining === 0) {
                                    payment.status = 'مدفوع';
                                } else if (paidAmount > 0) {
                                    payment.status = 'جزئي';
                                } else {
                                    payment.status = 'غير مدفوع';
                                }
                            }
                        });

                        // إضافة الرسوم الإضافية
                        if (transportFee > 0 && !student.transportPayment) {
                            student.transportPayment = {
                                amount: 0,
                                remaining: transportFee,
                                dueAmount: transportFee,
                                status: 'غير مدفوع'
                            };
                            fixedCount++;
                        }

                        if (registrationFee > 0 && !student.registrationPayment) {
                            student.registrationPayment = {
                                amount: 0,
                                remaining: registrationFee,
                                dueAmount: registrationFee,
                                status: 'غير مدفوع'
                            };
                            fixedCount++;
                        }
                    });

                    localStorage.setItem('sbea_students', JSON.stringify(students));
                    
                    showLoading(false);
                    showResult(`✅ تم إصلاح ${fixedCount} مشكلة في حسابات المدفوعات`, 'success');
                    updateStats();

                    // تحديث قائمة الإصلاحات
                    const appliedFixes = document.getElementById('applied-fixes');
                    appliedFixes.innerHTML += `<p>✅ إصلاح حسابات المدفوعات - ${new Date().toLocaleTimeString()}</p>`;

                } catch (error) {
                    showLoading(false);
                    showResult('❌ فشل في إصلاح المدفوعات: ' + error.message, 'error');
                }
            }, 1500);
        }

        function rebuildPaymentData() {
            if (!confirm('هل تريد إعادة بناء بيانات المدفوعات من الصفر؟ سيتم فقدان جميع المدفوعات المسجلة!')) {
                return;
            }

            showLoading(true);

            setTimeout(() => {
                try {
                    const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');

                    students.forEach(student => {
                        const monthlyFee = parseFloat(student.monthlyFee) || 0;
                        const transportFee = parseFloat(student.transportFee) || 0;
                        const registrationFee = parseFloat(student.registrationFee) || 0;

                        // إعادة بناء المدفوعات الشهرية
                        student.monthlyPayments = {};
                        ACADEMIC_MONTHS.forEach(month => {
                            student.monthlyPayments[month] = {
                                status: 'غير مدفوع',
                                amount: 0,
                                remaining: monthlyFee,
                                dueAmount: monthlyFee
                            };
                        });

                        // إعادة بناء رسوم النقل
                        if (transportFee > 0) {
                            student.transportPayment = {
                                amount: 0,
                                remaining: transportFee,
                                dueAmount: transportFee,
                                status: 'غير مدفوع'
                            };
                        }

                        // إعادة بناء رسوم التسجيل
                        if (registrationFee > 0) {
                            student.registrationPayment = {
                                amount: 0,
                                remaining: registrationFee,
                                dueAmount: registrationFee,
                                status: 'غير مدفوع'
                            };
                        }
                    });

                    localStorage.setItem('sbea_students', JSON.stringify(students));
                    
                    showLoading(false);
                    showResult('✅ تم إعادة بناء بيانات المدفوعات بنجاح', 'success');
                    updateStats();

                    const appliedFixes = document.getElementById('applied-fixes');
                    appliedFixes.innerHTML += `<p>✅ إعادة بناء بيانات المدفوعات - ${new Date().toLocaleTimeString()}</p>`;

                } catch (error) {
                    showLoading(false);
                    showResult('❌ فشل في إعادة البناء: ' + error.message, 'error');
                }
            }, 2000);
        }

        function updateAllStats() {
            showLoading(true);

            setTimeout(() => {
                updateStats();
                showLoading(false);
                showResult('✅ تم تحديث جميع الإحصائيات', 'success');

                const appliedFixes = document.getElementById('applied-fixes');
                appliedFixes.innerHTML += `<p>✅ تحديث الإحصائيات - ${new Date().toLocaleTimeString()}</p>`;
            }, 1000);
        }

        // تحميل الإحصائيات عند بدء الصفحة
        window.onload = function() {
            updateStats();
        };
    </script>
</body>
</html>

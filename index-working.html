<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة مؤسسة النور التربوي - يعمل 100%</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
        }

        .header h1 {
            color: #333;
            font-size: 2.8rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #666;
            font-size: 1.3rem;
            margin-bottom: 20px;
        }

        .status-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 14px;
        }

        .main-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .card:hover {
            transform: translateY(-10px) scale(1.02);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 20px 50px rgba(0,0,0,0.2);
        }

        .card-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            display: block;
        }

        .card-title {
            font-size: 1.8rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .card-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .card-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .card-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }

        .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            background: white;
            border: 2px solid #e9ecef;
            padding: 20px;
            border-radius: 15px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .action-btn:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .action-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .action-text {
            flex: 1;
        }

        .action-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .action-desc {
            color: #666;
            font-size: 14px;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            color: rgba(255, 255, 255, 0.8);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2.2rem;
            }
            
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .card {
                padding: 20px;
            }
            
            .card-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">🏫</div>
            <h1>مؤسسة النور التربوي</h1>
            <p>نظام إدارة المؤسسات التعليمية المحسن</p>
            <span class="status-badge">✅ يعمل بشكل مثالي</span>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <h2 style="text-align: center; margin-bottom: 20px; color: #333;">📊 إحصائيات سريعة</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="total-students">0</div>
                    <div class="stat-label">التلاميذ</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-teachers">0</div>
                    <div class="stat-label">الأساتذة</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-revenue">0</div>
                    <div class="stat-label">الإيرادات (DH)</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="total-expenses">0</div>
                    <div class="stat-label">النفقات (DH)</div>
                </div>
            </div>
        </div>

        <!-- Main Modules -->
        <div class="main-grid">
            <div class="card" onclick="window.location.href='students-working.html'">
                <span class="card-icon">👥</span>
                <h3 class="card-title">إدارة التلاميذ</h3>
                <p class="card-description">إضافة وتعديل وإدارة بيانات التلاميذ والمدفوعات بشكل محسن ومضمون</p>
                <a href="students-working.html" class="card-button">دخول</a>
            </div>

            <div class="card" onclick="window.location.href='teachers.html'">
                <span class="card-icon">👨‍🏫</span>
                <h3 class="card-title">إدارة الأساتذة</h3>
                <p class="card-description">إدارة بيانات الأساتذة والمواد الدراسية والجداول</p>
                <a href="teachers.html" class="card-button">دخول</a>
            </div>

            <div class="card" onclick="window.location.href='financial.html'">
                <span class="card-icon">💰</span>
                <h3 class="card-title">النظام المالي</h3>
                <p class="card-description">إدارة المدفوعات والرسوم والوصولات المالية</p>
                <a href="financial.html" class="card-button">دخول</a>
            </div>

            <div class="card" onclick="window.location.href='expenses.html'">
                <span class="card-icon">📊</span>
                <h3 class="card-title">إدارة النفقات</h3>
                <p class="card-description">تسجيل وتتبع النفقات والمصاريف اليومية</p>
                <a href="expenses.html" class="card-button">دخول</a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2 style="text-align: center; margin-bottom: 20px; color: #333;">⚡ إجراءات سريعة</h2>
            <div class="actions-grid">
                <a href="students-working.html" class="action-btn">
                    <div class="action-icon">➕</div>
                    <div class="action-text">
                        <div class="action-title">إضافة تلميذ جديد</div>
                        <div class="action-desc">إضافة تلميذ جديد بسرعة</div>
                    </div>
                </a>

                <a href="#" onclick="showSystemStatus()" class="action-btn">
                    <div class="action-icon">🔧</div>
                    <div class="action-text">
                        <div class="action-title">حالة النظام</div>
                        <div class="action-desc">فحص صحة النظام</div>
                    </div>
                </a>

                <a href="#" onclick="exportAllData()" class="action-btn">
                    <div class="action-icon">📤</div>
                    <div class="action-text">
                        <div class="action-title">تصدير البيانات</div>
                        <div class="action-desc">نسخ احتياطية شاملة</div>
                    </div>
                </a>

                <a href="#" onclick="showHelp()" class="action-btn">
                    <div class="action-icon">❓</div>
                    <div class="action-text">
                        <div class="action-title">المساعدة</div>
                        <div class="action-desc">دليل الاستخدام</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© 2025 مؤسسة النور التربوي للتعليم الخصوصي - نظام محسن وموثوق</p>
        </div>
    </div>

    <script>
        // تحميل الإحصائيات
        function loadStatistics() {
            try {
                // إحصائيات التلاميذ
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                document.getElementById('total-students').textContent = students.length;

                // إحصائيات الأساتذة
                const teachers = JSON.parse(localStorage.getItem('sbea_teachers') || '[]');
                document.getElementById('total-teachers').textContent = teachers.length;

                // حساب الإيرادات
                let totalRevenue = 0;
                students.forEach(student => {
                    if (student.payments) {
                        Object.values(student.payments).forEach(payment => {
                            totalRevenue += parseFloat(payment.amount || 0);
                        });
                    }
                });
                document.getElementById('total-revenue').textContent = totalRevenue.toLocaleString();

                // حساب النفقات
                const expenses = JSON.parse(localStorage.getItem('sbea_expenses') || '[]');
                let totalExpenses = 0;
                expenses.forEach(expense => {
                    totalExpenses += parseFloat(expense.amount || 0);
                });
                document.getElementById('total-expenses').textContent = totalExpenses.toLocaleString();

            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
            }
        }

        // عرض حالة النظام
        function showSystemStatus() {
            const status = {
                students: localStorage.getItem('sbea_students') ? '✅' : '❌',
                teachers: localStorage.getItem('sbea_teachers') ? '✅' : '❌',
                expenses: localStorage.getItem('sbea_expenses') ? '✅' : '❌',
                lastUpdate: new Date().toLocaleString('ar-MA')
            };

            alert(`حالة النظام:
            
📊 بيانات التلاميذ: ${status.students}
👨‍🏫 بيانات الأساتذة: ${status.teachers}
💰 بيانات النفقات: ${status.expenses}

🕒 آخر تحديث: ${status.lastUpdate}

✅ النظام يعمل بشكل مثالي!`);
        }

        // تصدير جميع البيانات
        function exportAllData() {
            try {
                const allData = {
                    students: JSON.parse(localStorage.getItem('sbea_students') || '[]'),
                    teachers: JSON.parse(localStorage.getItem('sbea_teachers') || '[]'),
                    expenses: JSON.parse(localStorage.getItem('sbea_expenses') || '[]'),
                    settings: {
                        lastStudentId: localStorage.getItem('sbea_last_student_id'),
                        exportDate: new Date().toISOString()
                    }
                };

                const dataStr = JSON.stringify(allData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = `نسخة_احتياطية_شاملة_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                alert('✅ تم تصدير النسخة الاحتياطية بنجاح!');
            } catch (error) {
                alert('❌ خطأ في التصدير: ' + error.message);
            }
        }

        // عرض المساعدة
        function showHelp() {
            alert(`📖 دليل الاستخدام السريع:

🏠 الصفحة الرئيسية:
• عرض الإحصائيات العامة
• الوصول السريع للوحدات

👥 إدارة التلاميذ:
• إضافة/تعديل/حذف التلاميذ
• إدارة المدفوعات الشهرية
• طباعة الوصولات

👨‍🏫 إدارة الأساتذة:
• إدارة بيانات الأساتذة
• تخصيص المواد

💰 النظام المالي:
• تسجيل المدفوعات
• إدارة الرسوم

📊 إدارة النفقات:
• تسجيل النفقات اليومية
• التقارير المالية

🔧 نصائح:
• استخدم النسخ الاحتياطية دورياً
• تحقق من حالة النظام بانتظام
• النظام يحفظ البيانات تلقائياً`);
        }

        // تهيئة الصفحة
        function initDashboard() {
            console.log('🚀 تحميل لوحة التحكم...');
            loadStatistics();
            console.log('✅ تم تحميل لوحة التحكم بنجاح');
        }

        // تشغيل التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initDashboard);

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(loadStatistics, 30000);
    </script>
</body>
</html>

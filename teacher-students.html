<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التلاميذ للأساتذة - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="teacher-students.html" class="active">تلاميذي</a></li>
                <li><a href="grades.html">النقاط والتقييمات</a></li>
                <li><a href="attendance.html">الحضور والغياب</a></li>
                <li><a href="homework.html">الواجبات المنزلية</a></li>
                <li><a href="reports.html">التقارير</a></li>
                <li><a href="profile.html">الملف الشخصي</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">الأستاذ</span>
            <span class="current-user-role">أستاذ</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="teacher-students-management">
            <h2>👨‍🏫 إدارة التلاميذ - واجهة الأساتذة</h2>
            
            <!-- فلاتر البحث والعرض -->
            <div class="teacher-filters">
                <div class="filter-group">
                    <label for="filter-level">المستوى</label>
                    <select id="filter-level">
                        <option value="">جميع المستويات</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-group">الفوج</label>
                    <select id="filter-group">
                        <option value="">جميع الأفواج</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-subject">المادة</label>
                    <select id="filter-subject">
                        <option value="">جميع المواد</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="search-student">البحث عن تلميذ</label>
                    <input type="text" id="search-student" placeholder="ابحث بالاسم أو الرقم التسلسلي...">
                </div>
                
                <div class="filter-actions">
                    <button id="apply-teacher-filters-btn" class="filter-btn">
                        <i class="fas fa-filter"></i> تطبيق الفلاتر
                    </button>
                    <button id="clear-teacher-filters-btn" class="filter-btn secondary">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="teacher-stats">
                <div class="stat-card primary">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h4>إجمالي التلاميذ</h4>
                        <p id="total-my-students">0</p>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h4>متوسط النقاط</h4>
                        <p id="average-grades">0.0</p>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">📝</div>
                    <div class="stat-content">
                        <h4>الواجبات المعلقة</h4>
                        <p id="pending-homework">0</p>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">📅</div>
                    <div class="stat-content">
                        <h4>الحضور اليوم</h4>
                        <p id="today-attendance">0%</p>
                    </div>
                </div>
            </div>

            <!-- إجراءات سريعة -->
            <div class="teacher-actions">
                <button id="add-grade-btn" class="action-btn primary">
                    <i class="fas fa-plus"></i> إضافة نقاط
                </button>
                <button id="add-note-btn" class="action-btn info">
                    <i class="fas fa-sticky-note"></i> إضافة ملاحظة
                </button>
                <button id="mark-attendance-btn" class="action-btn success">
                    <i class="fas fa-check"></i> تسجيل الحضور
                </button>
                <button id="assign-homework-btn" class="action-btn warning">
                    <i class="fas fa-book"></i> تكليف واجب
                </button>
                <button id="export-class-report-btn" class="action-btn secondary">
                    <i class="fas fa-download"></i> تصدير تقرير الفصل
                </button>
            </div>

            <!-- قائمة التلاميذ -->
            <div class="students-grid-container">
                <div class="view-toggle">
                    <button id="grid-view-btn" class="view-btn active">
                        <i class="fas fa-th"></i> عرض شبكي
                    </button>
                    <button id="list-view-btn" class="view-btn">
                        <i class="fas fa-list"></i> عرض قائمة
                    </button>
                </div>
                
                <!-- العرض الشبكي -->
                <div id="students-grid" class="students-grid">
                    <!-- سيتم ملء بطاقات التلاميذ هنا -->
                </div>
                
                <!-- عرض القائمة -->
                <div id="students-list" class="students-list" style="display: none;">
                    <!-- سيتم ملء جدول التلاميذ هنا -->
                </div>
            </div>
        </section>
    </main>

    <!-- نافذة إضافة نقاط -->
    <div id="add-grade-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📊 إضافة نقاط</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-grade-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="grade-student">التلميذ</label>
                            <select id="grade-student" required>
                                <option value="">اختر التلميذ</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="grade-subject">المادة</label>
                            <select id="grade-subject" required>
                                <option value="">اختر المادة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="grade-type">نوع التقييم</label>
                            <select id="grade-type" required>
                                <option value="">اختر النوع</option>
                                <option value="فرض">فرض</option>
                                <option value="امتحان">امتحان</option>
                                <option value="مشاركة">مشاركة</option>
                                <option value="واجب">واجب منزلي</option>
                                <option value="مشروع">مشروع</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="grade-value">النقطة</label>
                            <input type="number" id="grade-value" min="0" max="20" step="0.25" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="grade-date">التاريخ</label>
                            <input type="date" id="grade-date" required>
                        </div>
                        <div class="form-group">
                            <label for="grade-coefficient">المعامل</label>
                            <input type="number" id="grade-coefficient" min="1" max="5" value="1" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="grade-notes">ملاحظات</label>
                        <textarea id="grade-notes" placeholder="ملاحظات حول التقييم (اختياري)"></textarea>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="submit" class="submit-btn">إضافة النقطة</button>
                        <button type="button" class="cancel-btn">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة ملاحظة -->
    <div id="add-note-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📝 إضافة ملاحظة</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="add-note-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="note-student">التلميذ</label>
                            <select id="note-student" required>
                                <option value="">اختر التلميذ</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="note-type">نوع الملاحظة</label>
                            <select id="note-type" required>
                                <option value="">اختر النوع</option>
                                <option value="إيجابية">إيجابية</option>
                                <option value="سلبية">سلبية</option>
                                <option value="تحذير">تحذير</option>
                                <option value="تشجيع">تشجيع</option>
                                <option value="عامة">عامة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="note-subject">المادة المتعلقة</label>
                        <select id="note-subject">
                            <option value="">غير محدد</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="note-content">محتوى الملاحظة</label>
                        <textarea id="note-content" placeholder="اكتب الملاحظة هنا..." required></textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="note-date">التاريخ</label>
                            <input type="date" id="note-date" required>
                        </div>
                        <div class="form-group">
                            <label for="note-priority">الأولوية</label>
                            <select id="note-priority">
                                <option value="عادية">عادية</option>
                                <option value="مهمة">مهمة</option>
                                <option value="عاجلة">عاجلة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="note-notify-parent"> إشعار ولي الأمر
                        </label>
                    </div>
                    
                    <div class="modal-actions">
                        <button type="submit" class="submit-btn">إضافة الملاحظة</button>
                        <button type="button" class="cancel-btn">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل التلميذ -->
    <div id="student-details-modal" class="modal" style="display: none;">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>👤 تفاصيل التلميذ</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="student-details-content">
                    <!-- سيتم ملء تفاصيل التلميذ هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تسجيل الحضور -->
    <div id="attendance-modal" class="modal" style="display: none;">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>📅 تسجيل الحضور</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="attendance-controls">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="attendance-date">التاريخ</label>
                            <input type="date" id="attendance-date" required>
                        </div>
                        <div class="form-group">
                            <label for="attendance-subject">المادة</label>
                            <select id="attendance-subject" required>
                                <option value="">اختر المادة</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="attendance-period">الحصة</label>
                            <select id="attendance-period" required>
                                <option value="">اختر الحصة</option>
                                <option value="الأولى">الأولى</option>
                                <option value="الثانية">الثانية</option>
                                <option value="الثالثة">الثالثة</option>
                                <option value="الرابعة">الرابعة</option>
                                <option value="الخامسة">الخامسة</option>
                                <option value="السادسة">السادسة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="attendance-actions">
                        <button id="mark-all-present-btn" class="action-btn success">
                            <i class="fas fa-check-double"></i> تسجيل الكل حاضر
                        </button>
                        <button id="mark-all-absent-btn" class="action-btn danger">
                            <i class="fas fa-times-circle"></i> تسجيل الكل غائب
                        </button>
                        <button id="save-attendance-btn" class="action-btn primary">
                            <i class="fas fa-save"></i> حفظ الحضور
                        </button>
                    </div>
                </div>
                
                <div id="attendance-list" class="attendance-grid">
                    <!-- سيتم ملء قائمة الحضور هنا -->
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <!-- تضمين ملفات JavaScript -->
    <script src="auth.js"></script>
    <script src="loading.js"></script>
    <script src="notifications.js"></script>
    <script src="validation.js"></script>
    <script src="animations.js"></script>
    <script src="sync.js"></script>
    <script src="auto-logging.js"></script>
    <script src="receipt-printing.js"></script>
    <script src="script.js"></script>
</body>
</html>

// ===================================================================
//                    نظام النقاط والملاحظات الشامل
// ===================================================================

class GradesAndNotesSystem {
    constructor() {
        this.grades = [];
        this.notes = [];
        this.subjects = [
            'الرياضيات', 'اللغة العربية', 'اللغة الفرنسية', 'اللغة الإنجليزية',
            'النشاط العلمي', 'الاجتماعيات', 'التربية الإسلامية', 'التربية الفنية',
            'التربية البدنية', 'الفيزياء والكيمياء', 'علوم الحياة والأرض'
        ];
        this.gradeTypes = ['فرض', 'امتحان', 'مشاركة', 'واجب', 'مشروع'];
        this.noteTypes = ['إيجابية', 'سلبية', 'تحذير', 'تشجيع', 'عامة'];
        this.init();
    }

    // تهيئة النظام
    init() {
        this.loadData();
        this.setupEventHandlers();
    }

    // تحميل البيانات
    loadData() {
        try {
            this.grades = JSON.parse(localStorage.getItem('sbea_grades') || '[]');
            this.notes = JSON.parse(localStorage.getItem('sbea_notes') || '[]');
        } catch (error) {
            console.warn('خطأ في تحميل بيانات النقاط والملاحظات:', error);
            this.grades = [];
            this.notes = [];
        }
    }

    // حفظ البيانات
    saveData() {
        try {
            localStorage.setItem('sbea_grades', JSON.stringify(this.grades));
            localStorage.setItem('sbea_notes', JSON.stringify(this.notes));
        } catch (error) {
            console.warn('خطأ في حفظ بيانات النقاط والملاحظات:', error);
        }
    }

    // إعداد معالجات الأحداث
    setupEventHandlers() {
        // معالج إضافة نقطة
        document.addEventListener('submit', (event) => {
            if (event.target.id === 'add-grade-form') {
                event.preventDefault();
                this.handleAddGrade(event.target);
            }
        });

        // معالج إضافة ملاحظة
        document.addEventListener('submit', (event) => {
            if (event.target.id === 'add-note-form') {
                event.preventDefault();
                this.handleAddNote(event.target);
            }
        });
    }

    // إضافة نقطة جديدة
    addGrade(gradeData) {
        const grade = {
            id: this.generateId(),
            studentBarcode: gradeData.studentBarcode,
            studentName: gradeData.studentName,
            subject: gradeData.subject,
            type: gradeData.type,
            value: parseFloat(gradeData.value),
            maxValue: gradeData.maxValue || 20,
            coefficient: parseInt(gradeData.coefficient) || 1,
            date: gradeData.date,
            notes: gradeData.notes || '',
            teacherId: gradeData.teacherId,
            teacherName: gradeData.teacherName,
            createdAt: new Date().toISOString(),
            academicYear: this.getCurrentAcademicYear(),
            semester: this.getCurrentSemester()
        };

        this.grades.push(grade);
        this.saveData();

        // تسجيل النشاط
        if (window.autoLoggingSystem) {
            window.autoLoggingSystem.log('إنشاء', `إضافة نقطة للتلميذ: ${grade.studentName}`, {
                subject: grade.subject,
                type: grade.type,
                value: grade.value,
                studentBarcode: grade.studentBarcode
            });
        }

        return grade;
    }

    // إضافة ملاحظة جديدة
    addNote(noteData) {
        const note = {
            id: this.generateId(),
            studentBarcode: noteData.studentBarcode,
            studentName: noteData.studentName,
            type: noteData.type,
            subject: noteData.subject || '',
            content: noteData.content,
            priority: noteData.priority || 'عادية',
            date: noteData.date,
            notifyParent: noteData.notifyParent || false,
            teacherId: noteData.teacherId,
            teacherName: noteData.teacherName,
            createdAt: new Date().toISOString(),
            academicYear: this.getCurrentAcademicYear(),
            isRead: false
        };

        this.notes.push(note);
        this.saveData();

        // إرسال إشعار لولي الأمر إذا كان مطلوباً
        if (note.notifyParent) {
            this.notifyParent(note);
        }

        // تسجيل النشاط
        if (window.autoLoggingSystem) {
            window.autoLoggingSystem.log('إنشاء', `إضافة ملاحظة للتلميذ: ${note.studentName}`, {
                type: note.type,
                priority: note.priority,
                studentBarcode: note.studentBarcode,
                notifyParent: note.notifyParent
            });
        }

        return note;
    }

    // معالج إضافة نقطة من النموذج
    handleAddGrade(form) {
        const formData = new FormData(form);
        const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
        const selectedStudent = students.find(s => s.barcode === formData.get('student'));
        
        if (!selectedStudent) {
            window.showError('لم يتم العثور على التلميذ المحدد', 'خطأ');
            return;
        }

        const gradeData = {
            studentBarcode: selectedStudent.barcode,
            studentName: selectedStudent.name,
            subject: document.getElementById('grade-subject').value,
            type: document.getElementById('grade-type').value,
            value: document.getElementById('grade-value').value,
            coefficient: document.getElementById('grade-coefficient').value,
            date: document.getElementById('grade-date').value,
            notes: document.getElementById('grade-notes').value,
            teacherId: this.getCurrentTeacherId(),
            teacherName: this.getCurrentTeacherName()
        };

        // التحقق من صحة البيانات
        if (!gradeData.subject || !gradeData.type || !gradeData.value || !gradeData.date) {
            window.showError('يرجى ملء جميع الحقول المطلوبة', 'خطأ في البيانات');
            return;
        }

        if (gradeData.value < 0 || gradeData.value > 20) {
            window.showError('النقطة يجب أن تكون بين 0 و 20', 'خطأ في البيانات');
            return;
        }

        const grade = this.addGrade(gradeData);
        
        // إعادة تعيين النموذج
        form.reset();
        document.getElementById('grade-date').value = new Date().toISOString().split('T')[0];
        
        // إغلاق النافذة المنبثقة
        document.getElementById('add-grade-modal').style.display = 'none';
        
        // عرض رسالة نجاح
        window.showSuccess(`تم إضافة النقطة بنجاح للتلميذ ${gradeData.studentName}`, 'تمت الإضافة');
        
        // تحديث العرض
        this.refreshStudentDisplay();
    }

    // معالج إضافة ملاحظة من النموذج
    handleAddNote(form) {
        const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
        const selectedStudent = students.find(s => s.barcode === document.getElementById('note-student').value);
        
        if (!selectedStudent) {
            window.showError('لم يتم العثور على التلميذ المحدد', 'خطأ');
            return;
        }

        const noteData = {
            studentBarcode: selectedStudent.barcode,
            studentName: selectedStudent.name,
            type: document.getElementById('note-type').value,
            subject: document.getElementById('note-subject').value,
            content: document.getElementById('note-content').value,
            priority: document.getElementById('note-priority').value,
            date: document.getElementById('note-date').value,
            notifyParent: document.getElementById('note-notify-parent').checked,
            teacherId: this.getCurrentTeacherId(),
            teacherName: this.getCurrentTeacherName()
        };

        // التحقق من صحة البيانات
        if (!noteData.type || !noteData.content || !noteData.date) {
            window.showError('يرجى ملء جميع الحقول المطلوبة', 'خطأ في البيانات');
            return;
        }

        const note = this.addNote(noteData);
        
        // إعادة تعيين النموذج
        form.reset();
        document.getElementById('note-date').value = new Date().toISOString().split('T')[0];
        
        // إغلاق النافذة المنبثقة
        document.getElementById('add-note-modal').style.display = 'none';
        
        // عرض رسالة نجاح
        window.showSuccess(`تم إضافة الملاحظة بنجاح للتلميذ ${noteData.studentName}`, 'تمت الإضافة');
        
        // تحديث العرض
        this.refreshStudentDisplay();
    }

    // الحصول على نقاط تلميذ معين
    getStudentGrades(studentBarcode, filters = {}) {
        let grades = this.grades.filter(grade => grade.studentBarcode === studentBarcode);

        // تطبيق الفلاتر
        if (filters.subject) {
            grades = grades.filter(grade => grade.subject === filters.subject);
        }
        if (filters.type) {
            grades = grades.filter(grade => grade.type === filters.type);
        }
        if (filters.semester) {
            grades = grades.filter(grade => grade.semester === filters.semester);
        }
        if (filters.startDate) {
            grades = grades.filter(grade => grade.date >= filters.startDate);
        }
        if (filters.endDate) {
            grades = grades.filter(grade => grade.date <= filters.endDate);
        }

        return grades.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    // الحصول على ملاحظات تلميذ معين
    getStudentNotes(studentBarcode, filters = {}) {
        let notes = this.notes.filter(note => note.studentBarcode === studentBarcode);

        // تطبيق الفلاتر
        if (filters.type) {
            notes = notes.filter(note => note.type === filters.type);
        }
        if (filters.subject) {
            notes = notes.filter(note => note.subject === filters.subject);
        }
        if (filters.priority) {
            notes = notes.filter(note => note.priority === filters.priority);
        }
        if (filters.startDate) {
            notes = notes.filter(note => note.date >= filters.startDate);
        }
        if (filters.endDate) {
            notes = notes.filter(note => note.date <= filters.endDate);
        }

        return notes.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    // حساب متوسط نقاط تلميذ
    calculateStudentAverage(studentBarcode, subject = null, semester = null) {
        const filters = {};
        if (subject) filters.subject = subject;
        if (semester) filters.semester = semester;
        
        const grades = this.getStudentGrades(studentBarcode, filters);
        
        if (grades.length === 0) return 0;

        let totalPoints = 0;
        let totalCoefficients = 0;

        grades.forEach(grade => {
            totalPoints += grade.value * grade.coefficient;
            totalCoefficients += grade.coefficient;
        });

        return totalCoefficients > 0 ? (totalPoints / totalCoefficients).toFixed(2) : 0;
    }

    // حساب متوسط الفصل
    calculateClassAverage(level, group, subject = null, semester = null) {
        const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
        const classStudents = students.filter(s => s.level === level && s.group === group);
        
        if (classStudents.length === 0) return 0;

        let totalAverage = 0;
        let studentsWithGrades = 0;

        classStudents.forEach(student => {
            const average = this.calculateStudentAverage(student.barcode, subject, semester);
            if (average > 0) {
                totalAverage += parseFloat(average);
                studentsWithGrades++;
            }
        });

        return studentsWithGrades > 0 ? (totalAverage / studentsWithGrades).toFixed(2) : 0;
    }

    // إشعار ولي الأمر
    notifyParent(note) {
        // هذه الوظيفة يمكن تطويرها لإرسال إشعارات عبر WhatsApp أو SMS
        console.log('إشعار ولي الأمر:', note);
        
        // يمكن إضافة تكامل مع نظام WhatsApp الموجود
        if (window.whatsappSystem) {
            const message = `
إشعار من مؤسسة النور التربوي

التلميذ: ${note.studentName}
نوع الملاحظة: ${note.type}
المادة: ${note.subject || 'عامة'}
التاريخ: ${new Date(note.date).toLocaleDateString('ar-MA')}

الملاحظة:
${note.content}

الأستاذ: ${note.teacherName}
            `.trim();
            
            // يمكن إرسال الرسالة هنا
        }
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // الحصول على السنة الدراسية الحالية
    getCurrentAcademicYear() {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        
        if (currentMonth >= 9) {
            return `${currentYear}-${currentYear + 1}`;
        } else {
            return `${currentYear - 1}-${currentYear}`;
        }
    }

    // الحصول على الفصل الحالي
    getCurrentSemester() {
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        
        if (currentMonth >= 9 || currentMonth <= 1) {
            return 'الأول';
        } else if (currentMonth >= 2 && currentMonth <= 6) {
            return 'الثاني';
        } else {
            return 'الصيف';
        }
    }

    // الحصول على معرف الأستاذ الحالي
    getCurrentTeacherId() {
        if (window.authSystem && window.authSystem.getCurrentUser) {
            return window.authSystem.getCurrentUser()?.id || 'unknown';
        }
        return 'guest';
    }

    // الحصول على اسم الأستاذ الحالي
    getCurrentTeacherName() {
        if (window.authSystem && window.authSystem.getCurrentUser) {
            return window.authSystem.getCurrentUser()?.username || 'مجهول';
        }
        return 'ضيف';
    }

    // تحديث عرض التلاميذ
    refreshStudentDisplay() {
        // إعادة تحميل البيانات وتحديث العرض
        if (typeof displayStudentsGrid === 'function') {
            displayStudentsGrid();
        }
        if (typeof updateTeacherStats === 'function') {
            updateTeacherStats();
        }
    }

    // حذف نقطة
    deleteGrade(gradeId) {
        const gradeIndex = this.grades.findIndex(grade => grade.id === gradeId);
        if (gradeIndex > -1) {
            const deletedGrade = this.grades[gradeIndex];
            this.grades.splice(gradeIndex, 1);
            this.saveData();
            
            // تسجيل النشاط
            if (window.autoLoggingSystem) {
                window.autoLoggingSystem.log('حذف', `حذف نقطة للتلميذ: ${deletedGrade.studentName}`, {
                    subject: deletedGrade.subject,
                    type: deletedGrade.type,
                    value: deletedGrade.value
                });
            }
            
            return true;
        }
        return false;
    }

    // حذف ملاحظة
    deleteNote(noteId) {
        const noteIndex = this.notes.findIndex(note => note.id === noteId);
        if (noteIndex > -1) {
            const deletedNote = this.notes[noteIndex];
            this.notes.splice(noteIndex, 1);
            this.saveData();
            
            // تسجيل النشاط
            if (window.autoLoggingSystem) {
                window.autoLoggingSystem.log('حذف', `حذف ملاحظة للتلميذ: ${deletedNote.studentName}`, {
                    type: deletedNote.type,
                    priority: deletedNote.priority
                });
            }
            
            return true;
        }
        return false;
    }

    // تصدير نقاط تلميذ
    exportStudentGrades(studentBarcode, format = 'json') {
        const grades = this.getStudentGrades(studentBarcode);
        const student = JSON.parse(localStorage.getItem('sbea_students') || '[]')
            .find(s => s.barcode === studentBarcode);
        
        if (!student) return;

        const timestamp = new Date().toISOString().split('T')[0];
        
        if (format === 'json') {
            const data = {
                student: student,
                grades: grades,
                exportDate: new Date().toISOString(),
                academicYear: this.getCurrentAcademicYear()
            };
            
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `نقاط_${student.name}_${timestamp}.json`;
            link.click();
        }
    }

    // تصدير ملاحظات تلميذ
    exportStudentNotes(studentBarcode, format = 'json') {
        const notes = this.getStudentNotes(studentBarcode);
        const student = JSON.parse(localStorage.getItem('sbea_students') || '[]')
            .find(s => s.barcode === studentBarcode);
        
        if (!student) return;

        const timestamp = new Date().toISOString().split('T')[0];
        
        if (format === 'json') {
            const data = {
                student: student,
                notes: notes,
                exportDate: new Date().toISOString(),
                academicYear: this.getCurrentAcademicYear()
            };
            
            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `ملاحظات_${student.name}_${timestamp}.json`;
            link.click();
        }
    }
}

// إنشاء مثيل النظام
window.gradesAndNotesSystem = new GradesAndNotesSystem();

// دوال مساعدة للاستخدام السهل
window.addGrade = (gradeData) => {
    return window.gradesAndNotesSystem.addGrade(gradeData);
};

window.addNote = (noteData) => {
    return window.gradesAndNotesSystem.addNote(noteData);
};

window.getStudentGrades = (studentBarcode, filters) => {
    return window.gradesAndNotesSystem.getStudentGrades(studentBarcode, filters);
};

window.getStudentNotes = (studentBarcode, filters) => {
    return window.gradesAndNotesSystem.getStudentNotes(studentBarcode, filters);
};

window.calculateStudentAverage = (studentBarcode, subject, semester) => {
    return window.gradesAndNotesSystem.calculateStudentAverage(studentBarcode, subject, semester);
};

// تصدير النظام
export { GradesAndNotesSystem };

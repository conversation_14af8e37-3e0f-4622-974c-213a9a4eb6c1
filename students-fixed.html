<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التلاميذ - مؤسسة النور التربوي (محسن)</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <style>
        /* إضافة أنماط للتحميل */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }
        .quick-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .quick-actions h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .action-btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .action-btn.primary { background: #007bff; color: white; }
        .action-btn.success { background: #28a745; color: white; }
        .action-btn.warning { background: #ffc107; color: #333; }
        .action-btn.danger { background: #dc3545; color: white; }
        .action-btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div>
            <div class="loading-spinner"></div>
            <p style="margin-top: 20px; text-align: center;">جاري تحميل بيانات التلاميذ...</p>
        </div>
    </div>

    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students-fixed.html" class="active">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="expenses.html">إدارة النفقات</a></li>
                <li><a href="teacher-students.html">واجهة الأساتذة</a></li>
                <li><a href="logs.html">سجلات النظام</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="student-management">
            <h2>إدارة التلاميذ (محسن)</h2>
            
            <!-- رسائل النظام -->
            <div id="system-messages"></div>

            <!-- الإجراءات السريعة -->
            <div class="quick-actions">
                <h3>الإجراءات السريعة</h3>
                <button class="action-btn primary" onclick="addNewStudent()">➕ إضافة تلميذ جديد</button>
                <button class="action-btn success" onclick="refreshStudentsList()">🔄 تحديث القائمة</button>
                <button class="action-btn warning" onclick="fixPaymentCalculations()">🔧 إصلاح المدفوعات</button>
                <button class="action-btn danger" onclick="openDiagnostics()">🧪 تشخيص المشاكل</button>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                <div class="stat-card" style="background: #007bff; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold;" id="total-students">0</div>
                    <div>إجمالي التلاميذ</div>
                </div>
                <div class="stat-card" style="background: #28a745; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold;" id="total-fees">0 DH</div>
                    <div>إجمالي الرسوم</div>
                </div>
                <div class="stat-card" style="background: #17a2b8; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold;" id="total-paid">0 DH</div>
                    <div>إجمالي المدفوع</div>
                </div>
                <div class="stat-card" style="background: #dc3545; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold;" id="total-remaining">0 DH</div>
                    <div>إجمالي الباقي</div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="search-filters" style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                <h3>البحث والفلترة</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <input type="text" id="search-name" placeholder="البحث بالاسم" style="padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    <input type="text" id="search-barcode" placeholder="البحث بالرقم التسلسلي" style="padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    <select id="search-level" style="padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        <option value="">جميع المستويات</option>
                    </select>
                    <button class="action-btn primary" onclick="applyFilters()">🔍 تطبيق الفلاتر</button>
                </div>
            </div>

            <!-- قائمة التلاميذ -->
            <div id="students-container">
                <div id="students-table-container" style="overflow-x: auto;">
                    <table id="students-table" style="width: 100%; border-collapse: collapse; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                        <thead style="background: #007bff; color: white;">
                            <tr>
                                <th style="padding: 15px; text-align: center;">الصورة</th>
                                <th style="padding: 15px; text-align: center;">الرقم التسلسلي</th>
                                <th style="padding: 15px; text-align: center;">الاسم</th>
                                <th style="padding: 15px; text-align: center;">المستوى</th>
                                <th style="padding: 15px; text-align: center;">الفوج</th>
                                <th style="padding: 15px; text-align: center;">الرسوم الشهرية</th>
                                <th style="padding: 15px; text-align: center;">النقل</th>
                                <th style="padding: 15px; text-align: center;">المدفوع</th>
                                <th style="padding: 15px; text-align: center;">الباقي</th>
                                <th style="padding: 15px; text-align: center;">الهاتف</th>
                                <th style="padding: 15px; text-align: center;">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="students-table-body">
                            <!-- سيتم ملء البيانات هنا -->
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>

    <!-- تضمين ملفات JavaScript -->
    <script src="validation.js"></script>
    <script src="notifications.js"></script>
    <script src="loading.js"></script>
    <script src="animations.js"></script>
    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="auto-logging.js"></script>
    <script src="receipt-printing.js"></script>
    <script src="grades-system.js"></script>
    <script src="system-diagnostics.js"></script>
    <script src="script.js"></script>

    <script>
        // المتغيرات العامة
        let students = [];
        let filteredStudents = [];

        // الأشهر الدراسية
        const ACADEMIC_MONTHS = [
            "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر", "يناير",
            "فبراير", "مارس", "أبريل", "مايو", "يونيو"
        ];

        // المستويات الدراسية
        const ACADEMIC_LEVELS = [
            'التحضيري', 'الأول ابتدائي', 'الثاني ابتدائي', 'الثالث ابتدائي',
            'الرابع ابتدائي', 'الخامس ابتدائي', 'السادس ابتدائي',
            'الأولى إعدادي', 'الثانية إعدادي', 'الثالثة إعدادي',
            'الجذع المشترك', 'الأولى بكالوريا', 'الثانية بكالوريا'
        ];

        // دوال مساعدة
        function showMessage(message, type = 'info') {
            const messagesContainer = document.getElementById('system-messages');
            const messageClass = type === 'error' ? 'error-message' : 'success-message';
            
            messagesContainer.innerHTML = `<div class="${messageClass}">${message}</div>`;
            
            setTimeout(() => {
                messagesContainer.innerHTML = '';
            }, 5000);
        }

        function showLoading(show = true) {
            document.getElementById('loading-overlay').style.display = show ? 'flex' : 'none';
        }

        // تحميل البيانات
        function loadStudents() {
            try {
                students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                filteredStudents = [...students];
                return true;
            } catch (error) {
                console.error('خطأ في تحميل بيانات التلاميذ:', error);
                showMessage('خطأ في تحميل بيانات التلاميذ: ' + error.message, 'error');
                return false;
            }
        }

        // حساب الإحصائيات
        function calculateStats() {
            let totalFees = 0;
            let totalPaid = 0;
            let totalRemaining = 0;

            students.forEach(student => {
                const monthlyFee = parseFloat(student.monthlyFee || student.fee || 0);
                const transportFee = parseFloat(student.transportFee || 0);
                const registrationFee = parseFloat(student.registrationFee || 0);
                
                // إجمالي الرسوم السنوية
                const yearlyFees = (monthlyFee * 10) + transportFee + registrationFee;
                totalFees += yearlyFees;

                // حساب المدفوع
                let studentPaid = 0;
                if (student.monthlyPayments) {
                    Object.values(student.monthlyPayments).forEach(payment => {
                        studentPaid += parseFloat(payment.amount) || 0;
                    });
                }
                if (student.transportPayment) {
                    studentPaid += parseFloat(student.transportPayment.amount) || 0;
                }
                if (student.registrationPayment) {
                    studentPaid += parseFloat(student.registrationPayment.amount) || 0;
                }
                totalPaid += studentPaid;

                // حساب الباقي
                const studentRemaining = Math.max(0, yearlyFees - studentPaid);
                totalRemaining += studentRemaining;
            });

            // تحديث العرض
            document.getElementById('total-students').textContent = students.length;
            document.getElementById('total-fees').textContent = totalFees.toLocaleString() + ' DH';
            document.getElementById('total-paid').textContent = totalPaid.toLocaleString() + ' DH';
            document.getElementById('total-remaining').textContent = totalRemaining.toLocaleString() + ' DH';
        }

        // عرض التلاميذ
        function displayStudents() {
            const tbody = document.getElementById('students-table-body');
            tbody.innerHTML = '';

            if (filteredStudents.length === 0) {
                tbody.innerHTML = '<tr><td colspan="11" style="text-align: center; padding: 30px; color: #666;">لا توجد بيانات تلاميذ</td></tr>';
                return;
            }

            filteredStudents.forEach((student, index) => {
                const monthlyFee = parseFloat(student.monthlyFee || student.fee || 0);
                const transportFee = parseFloat(student.transportFee || 0);
                const registrationFee = parseFloat(student.registrationFee || 0);
                
                // حساب المدفوع والباقي
                let studentPaid = 0;
                if (student.monthlyPayments) {
                    Object.values(student.monthlyPayments).forEach(payment => {
                        studentPaid += parseFloat(payment.amount) || 0;
                    });
                }
                if (student.transportPayment) {
                    studentPaid += parseFloat(student.transportPayment.amount) || 0;
                }
                if (student.registrationPayment) {
                    studentPaid += parseFloat(student.registrationPayment.amount) || 0;
                }

                const yearlyFees = (monthlyFee * 10) + transportFee + registrationFee;
                const studentRemaining = Math.max(0, yearlyFees - studentPaid);

                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #e0e0e0';
                row.innerHTML = `
                    <td style="padding: 12px; text-align: center;">
                        <img src="${student.picture || 'logo.png'}" alt="صورة" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;" onerror="this.src='logo.png'">
                    </td>
                    <td style="padding: 12px; text-align: center; font-weight: bold;">${student.barcode}</td>
                    <td style="padding: 12px; text-align: center;">${student.name}</td>
                    <td style="padding: 12px; text-align: center;">${student.level}</td>
                    <td style="padding: 12px; text-align: center;">${student.group}</td>
                    <td style="padding: 12px; text-align: center;">${monthlyFee.toLocaleString()} DH</td>
                    <td style="padding: 12px; text-align: center; color: ${student.hasTransport ? '#28a745' : '#6c757d'};">
                        ${student.hasTransport ? `نعم (${transportFee} DH)` : 'لا'}
                    </td>
                    <td style="padding: 12px; text-align: center; color: #28a745; font-weight: bold;">${studentPaid.toLocaleString()} DH</td>
                    <td style="padding: 12px; text-align: center; color: ${studentRemaining > 0 ? '#dc3545' : '#28a745'}; font-weight: bold;">${studentRemaining.toLocaleString()} DH</td>
                    <td style="padding: 12px; text-align: center;">${student.phone || '-'}</td>
                    <td style="padding: 12px; text-align: center;">
                        <button class="action-btn primary" onclick="openPaymentModal('${student.barcode}')" title="إدارة المدفوعات">💰</button>
                        <button class="action-btn warning" onclick="editStudent('${student.barcode}')" title="تعديل">✏️</button>
                        <button class="action-btn danger" onclick="deleteStudent('${student.barcode}')" title="حذف">🗑️</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // تطبيق الفلاتر
        function applyFilters() {
            const nameFilter = document.getElementById('search-name').value.toLowerCase();
            const barcodeFilter = document.getElementById('search-barcode').value;
            const levelFilter = document.getElementById('search-level').value;

            filteredStudents = students.filter(student => {
                const nameMatch = !nameFilter || student.name.toLowerCase().includes(nameFilter);
                const barcodeMatch = !barcodeFilter || student.barcode.includes(barcodeFilter);
                const levelMatch = !levelFilter || student.level === levelFilter;
                
                return nameMatch && barcodeMatch && levelMatch;
            });

            displayStudents();
        }

        // تحديث قائمة التلاميذ
        function refreshStudentsList() {
            showLoading(true);
            
            setTimeout(() => {
                if (loadStudents()) {
                    displayStudents();
                    calculateStats();
                    showMessage('تم تحديث قائمة التلاميذ بنجاح', 'success');
                }
                showLoading(false);
            }, 1000);
        }

        // إضافة تلميذ جديد
        function addNewStudent() {
            const name = prompt('اسم التلميذ:');
            if (!name) return;

            const level = prompt('المستوى الدراسي:');
            if (!level) return;

            const group = prompt('الفوج:');
            if (!group) return;

            const monthlyFee = parseFloat(prompt('الرسوم الشهرية:') || '0');

            try {
                const lastId = parseInt(localStorage.getItem('sbea_last_student_id') || '0');
                const newId = lastId + 1;

                const newStudent = {
                    id: newId,
                    barcode: newId.toString(),
                    name: name,
                    level: level,
                    group: group,
                    monthlyFee: monthlyFee,
                    phone: '',
                    hasTransport: false,
                    transportFee: 0,
                    registrationFee: 0,
                    monthlyPayments: {},
                    createdAt: new Date().toISOString()
                };

                // إنشاء مدفوعات شهرية فارغة
                ACADEMIC_MONTHS.forEach(month => {
                    newStudent.monthlyPayments[month] = {
                        status: 'غير مدفوع',
                        amount: 0,
                        remaining: monthlyFee,
                        dueAmount: monthlyFee
                    };
                });

                students.push(newStudent);
                localStorage.setItem('sbea_students', JSON.stringify(students));
                localStorage.setItem('sbea_last_student_id', newId.toString());

                refreshStudentsList();
                showMessage(`تم إضافة التلميذ ${name} بنجاح (الرقم التسلسلي: ${newId})`, 'success');

            } catch (error) {
                showMessage('خطأ في إضافة التلميذ: ' + error.message, 'error');
            }
        }

        // إصلاح حسابات المدفوعات
        function fixPaymentCalculations() {
            if (!confirm('هل تريد إصلاح حسابات المدفوعات؟ سيتم إعادة حساب جميع المبالغ.')) {
                return;
            }

            showLoading(true);

            setTimeout(() => {
                try {
                    let fixedCount = 0;

                    students.forEach(student => {
                        const monthlyFee = parseFloat(student.monthlyFee || student.fee || 0);

                        // إنشاء مدفوعات شهرية إذا لم تكن موجودة
                        if (!student.monthlyPayments) {
                            student.monthlyPayments = {};
                            fixedCount++;
                        }

                        // إصلاح المدفوعات الشهرية
                        ACADEMIC_MONTHS.forEach(month => {
                            if (!student.monthlyPayments[month]) {
                                student.monthlyPayments[month] = {
                                    status: 'غير مدفوع',
                                    amount: 0,
                                    remaining: monthlyFee,
                                    dueAmount: monthlyFee
                                };
                                fixedCount++;
                            } else {
                                const payment = student.monthlyPayments[month];
                                const paidAmount = parseFloat(payment.amount) || 0;
                                payment.remaining = Math.max(0, monthlyFee - paidAmount);
                                payment.dueAmount = monthlyFee;
                                
                                if (payment.remaining === 0) {
                                    payment.status = 'مدفوع';
                                } else if (paidAmount > 0) {
                                    payment.status = 'جزئي';
                                } else {
                                    payment.status = 'غير مدفوع';
                                }
                            }
                        });
                    });

                    localStorage.setItem('sbea_students', JSON.stringify(students));
                    
                    refreshStudentsList();
                    showMessage(`تم إصلاح ${fixedCount} مشكلة في حسابات المدفوعات`, 'success');

                } catch (error) {
                    showMessage('فشل في إصلاح المدفوعات: ' + error.message, 'error');
                }
                
                showLoading(false);
            }, 1500);
        }

        // فتح نافذة الدفع
        function openPaymentModal(barcode) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) {
                showMessage('لم يتم العثور على التلميذ', 'error');
                return;
            }

            if (typeof window.openPaymentsModal === 'function') {
                window.openPaymentsModal(student);
            } else {
                alert(`إدارة مدفوعات التلميذ: ${student.name}\nالرقم التسلسلي: ${student.barcode}\n\nسيتم تطوير هذه الميزة قريباً.`);
            }
        }

        // تعديل تلميذ
        function editStudent(barcode) {
            alert('سيتم تطوير ميزة التعديل قريباً');
        }

        // حذف تلميذ
        function deleteStudent(barcode) {
            const student = students.find(s => s.barcode === barcode);
            if (!student) return;

            if (confirm(`هل أنت متأكد من حذف التلميذ: ${student.name}؟`)) {
                students = students.filter(s => s.barcode !== barcode);
                localStorage.setItem('sbea_students', JSON.stringify(students));
                refreshStudentsList();
                showMessage(`تم حذف التلميذ ${student.name} بنجاح`, 'success');
            }
        }

        // فتح صفحة التشخيص
        function openDiagnostics() {
            window.open('fix-students-payments.html', '_blank');
        }

        // تهيئة الصفحة
        function initializePage() {
            // ملء قائمة المستويات
            const levelSelect = document.getElementById('search-level');
            ACADEMIC_LEVELS.forEach(level => {
                const option = document.createElement('option');
                option.value = level;
                option.textContent = level;
                levelSelect.appendChild(option);
            });

            // إعداد معالجات الأحداث
            document.getElementById('search-name').addEventListener('input', applyFilters);
            document.getElementById('search-barcode').addEventListener('input', applyFilters);
            document.getElementById('search-level').addEventListener('change', applyFilters);

            // تحميل البيانات
            showLoading(true);
            setTimeout(() => {
                if (loadStudents()) {
                    displayStudents();
                    calculateStats();
                    showMessage('تم تحميل بيانات التلاميذ بنجاح', 'success');
                }
                showLoading(false);
            }, 1000);
        }

        // تشغيل التهيئة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>

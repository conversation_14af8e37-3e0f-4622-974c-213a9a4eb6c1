// ===================================================================
//                    نظام التحميل والحالات المتقدم
// ===================================================================

class LoadingSystem {
    constructor() {
        this.activeLoaders = new Map();
        this.init();
    }

    // تهيئة النظام
    init() {
        this.setupStyles();
        this.createGlobalLoader();
    }

    // إعداد الأنماط
    setupStyles() {
        if (document.getElementById('loading-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'loading-styles';
        styles.textContent = `
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .loading-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .loading-content {
                background: white;
                border-radius: 12px;
                padding: 30px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                min-width: 200px;
                transform: scale(0.8);
                transition: transform 0.3s ease;
            }

            .loading-overlay.show .loading-content {
                transform: scale(1);
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 15px;
            }

            .loading-dots {
                display: inline-flex;
                gap: 4px;
                margin: 0 auto 15px;
            }

            .loading-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #007bff;
                animation: loadingDots 1.4s ease-in-out infinite both;
            }

            .loading-dot:nth-child(1) { animation-delay: -0.32s; }
            .loading-dot:nth-child(2) { animation-delay: -0.16s; }

            .loading-progress {
                width: 100%;
                height: 6px;
                background: #f3f3f3;
                border-radius: 3px;
                overflow: hidden;
                margin: 15px 0;
            }

            .loading-progress-bar {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #0056b3);
                border-radius: 3px;
                transition: width 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .loading-progress-bar::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background-image: linear-gradient(
                    -45deg,
                    rgba(255, 255, 255, .2) 25%,
                    transparent 25%,
                    transparent 50%,
                    rgba(255, 255, 255, .2) 50%,
                    rgba(255, 255, 255, .2) 75%,
                    transparent 75%,
                    transparent
                );
                background-size: 50px 50px;
                animation: move 2s linear infinite;
            }

            .loading-text {
                color: #333;
                font-size: 14px;
                margin: 0;
            }

            .loading-subtext {
                color: #666;
                font-size: 12px;
                margin: 5px 0 0 0;
            }

            .btn-loading {
                position: relative;
                pointer-events: none;
                opacity: 0.7;
            }

            .btn-loading::after {
                content: '';
                position: absolute;
                width: 16px;
                height: 16px;
                margin: auto;
                border: 2px solid transparent;
                border-top-color: currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
            }

            .btn-loading .btn-text {
                opacity: 0;
            }

            .form-loading {
                position: relative;
                pointer-events: none;
            }

            .form-loading::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255,255,255,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
            }

            .table-loading {
                position: relative;
            }

            .table-loading::after {
                content: 'جاري التحميل...';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(255,255,255,0.95);
                padding: 20px 30px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                font-size: 14px;
                color: #666;
            }

            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
                border-radius: 4px;
            }

            .skeleton-text {
                height: 16px;
                margin: 8px 0;
            }

            .skeleton-title {
                height: 20px;
                width: 60%;
                margin: 12px 0;
            }

            .skeleton-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
            }

            .skeleton-button {
                height: 36px;
                width: 100px;
                border-radius: 6px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            @keyframes loadingDots {
                0%, 80%, 100% {
                    transform: scale(0);
                    opacity: 0.5;
                }
                40% {
                    transform: scale(1);
                    opacity: 1;
                }
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            @keyframes move {
                0% { background-position: 0 0; }
                100% { background-position: 50px 50px; }
            }

            .fade-in {
                animation: fadeIn 0.5s ease-in;
            }

            .fade-out {
                animation: fadeOut 0.5s ease-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(20px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes fadeOut {
                from { opacity: 1; transform: translateY(0); }
                to { opacity: 0; transform: translateY(-20px); }
            }

            .slide-in-right {
                animation: slideInRight 0.5s ease-out;
            }

            .slide-out-right {
                animation: slideOutRight 0.5s ease-in;
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); }
                to { transform: translateX(0); }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); }
                to { transform: translateX(100%); }
            }
        `;
        document.head.appendChild(styles);
    }

    // إنشاء محمل عام
    createGlobalLoader() {
        this.globalLoader = document.createElement('div');
        this.globalLoader.id = 'global-loader';
        this.globalLoader.className = 'loading-overlay';
        this.globalLoader.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p class="loading-text">جاري التحميل...</p>
                <p class="loading-subtext">يرجى الانتظار</p>
            </div>
        `;
        document.body.appendChild(this.globalLoader);
    }

    // عرض محمل عام
    showGlobal(text = 'جاري التحميل...', subtext = 'يرجى الانتظار') {
        const textElement = this.globalLoader.querySelector('.loading-text');
        const subtextElement = this.globalLoader.querySelector('.loading-subtext');
        
        if (textElement) textElement.textContent = text;
        if (subtextElement) subtextElement.textContent = subtext;
        
        this.globalLoader.classList.add('show');
        return 'global';
    }

    // إخفاء محمل عام
    hideGlobal() {
        this.globalLoader.classList.remove('show');
    }

    // عرض محمل مع شريط تقدم
    showProgress(text = 'جاري التحميل...', initialProgress = 0) {
        const loaderId = 'progress_' + Date.now();
        
        const loader = document.createElement('div');
        loader.id = loaderId;
        loader.className = 'loading-overlay show';
        loader.innerHTML = `
            <div class="loading-content">
                <div class="loading-progress">
                    <div class="loading-progress-bar" style="width: ${initialProgress}%"></div>
                </div>
                <p class="loading-text">${text}</p>
                <p class="loading-subtext">${initialProgress}%</p>
            </div>
        `;
        
        document.body.appendChild(loader);
        this.activeLoaders.set(loaderId, loader);
        
        return loaderId;
    }

    // تحديث شريط التقدم
    updateProgress(loaderId, progress, text = null) {
        const loader = this.activeLoaders.get(loaderId);
        if (!loader) return;
        
        const progressBar = loader.querySelector('.loading-progress-bar');
        const subtextElement = loader.querySelector('.loading-subtext');
        const textElement = loader.querySelector('.loading-text');
        
        if (progressBar) {
            progressBar.style.width = `${Math.min(100, Math.max(0, progress))}%`;
        }
        
        if (subtextElement) {
            subtextElement.textContent = `${Math.round(progress)}%`;
        }
        
        if (text && textElement) {
            textElement.textContent = text;
        }
    }

    // إخفاء محمل
    hide(loaderId) {
        if (loaderId === 'global') {
            this.hideGlobal();
            return;
        }
        
        const loader = this.activeLoaders.get(loaderId);
        if (!loader) return;
        
        loader.classList.remove('show');
        
        setTimeout(() => {
            if (loader.parentNode) {
                loader.parentNode.removeChild(loader);
            }
            this.activeLoaders.delete(loaderId);
        }, 300);
    }

    // تحميل زر
    loadButton(button, text = null) {
        if (!button) return;
        
        button.classList.add('btn-loading');
        button.disabled = true;
        
        if (text) {
            button.setAttribute('data-original-text', button.textContent);
            button.innerHTML = `<span class="btn-text">${text}</span>`;
        }
    }

    // إيقاف تحميل زر
    unloadButton(button) {
        if (!button) return;
        
        button.classList.remove('btn-loading');
        button.disabled = false;
        
        const originalText = button.getAttribute('data-original-text');
        if (originalText) {
            button.textContent = originalText;
            button.removeAttribute('data-original-text');
        }
    }

    // تحميل نموذج
    loadForm(form) {
        if (!form) return;
        form.classList.add('form-loading');
    }

    // إيقاف تحميل نموذج
    unloadForm(form) {
        if (!form) return;
        form.classList.remove('form-loading');
    }

    // تحميل جدول
    loadTable(table) {
        if (!table) return;
        table.classList.add('table-loading');
    }

    // إيقاف تحميل جدول
    unloadTable(table) {
        if (!table) return;
        table.classList.remove('table-loading');
    }

    // إنشاء هيكل عظمي
    createSkeleton(container, type = 'default') {
        if (!container) return;
        
        const skeletons = {
            default: `
                <div class="skeleton skeleton-title"></div>
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text" style="width: 80%;"></div>
                <div class="skeleton skeleton-text" style="width: 60%;"></div>
            `,
            card: `
                <div style="display: flex; gap: 15px; margin-bottom: 15px;">
                    <div class="skeleton skeleton-avatar"></div>
                    <div style="flex: 1;">
                        <div class="skeleton skeleton-title"></div>
                        <div class="skeleton skeleton-text"></div>
                    </div>
                </div>
            `,
            table: `
                <div class="skeleton skeleton-text" style="height: 40px; margin-bottom: 10px;"></div>
                <div class="skeleton skeleton-text" style="height: 40px; margin-bottom: 10px;"></div>
                <div class="skeleton skeleton-text" style="height: 40px; margin-bottom: 10px;"></div>
            `,
            form: `
                <div class="skeleton skeleton-text" style="height: 20px; width: 30%; margin-bottom: 5px;"></div>
                <div class="skeleton skeleton-text" style="height: 40px; margin-bottom: 20px;"></div>
                <div class="skeleton skeleton-text" style="height: 20px; width: 30%; margin-bottom: 5px;"></div>
                <div class="skeleton skeleton-text" style="height: 40px; margin-bottom: 20px;"></div>
                <div class="skeleton skeleton-button"></div>
            `
        };
        
        container.innerHTML = skeletons[type] || skeletons.default;
    }

    // إزالة هيكل عظمي
    removeSkeleton(container, content) {
        if (!container) return;
        
        container.classList.add('fade-out');
        
        setTimeout(() => {
            container.innerHTML = content;
            container.classList.remove('fade-out');
            container.classList.add('fade-in');
            
            setTimeout(() => {
                container.classList.remove('fade-in');
            }, 500);
        }, 250);
    }

    // محاكاة تحميل مع تقدم
    simulateProgress(text = 'جاري التحميل...', duration = 3000) {
        return new Promise((resolve) => {
            const loaderId = this.showProgress(text, 0);
            let progress = 0;
            const increment = 100 / (duration / 50);
            
            const interval = setInterval(() => {
                progress += increment;
                
                if (progress >= 100) {
                    progress = 100;
                    this.updateProgress(loaderId, progress, 'تم الانتهاء');
                    
                    setTimeout(() => {
                        this.hide(loaderId);
                        resolve();
                    }, 500);
                    
                    clearInterval(interval);
                } else {
                    this.updateProgress(loaderId, progress);
                }
            }, 50);
        });
    }
}

// إنشاء مثيل عام للنظام
window.loadingSystem = new LoadingSystem();

// اختصارات سريعة
window.showLoading = (text, subtext) => window.loadingSystem.showGlobal(text, subtext);
window.hideLoading = () => window.loadingSystem.hideGlobal();
window.showProgress = (text, progress) => window.loadingSystem.showProgress(text, progress);
window.updateProgress = (id, progress, text) => window.loadingSystem.updateProgress(id, progress, text);
window.hideProgress = (id) => window.loadingSystem.hide(id);

// تصدير النظام
export { LoadingSystem };

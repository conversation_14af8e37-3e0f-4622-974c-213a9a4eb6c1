// ===================================================================
//                    نظام المصادقة والصلاحيات
// ===================================================================

// المستخدمون الافتراضيون
const DEFAULT_USERS = [
    {
        id: 'admin001',
        username: 'admin',
        password: 'admin123',
        name: 'المدير العام',
        email: '<EMAIL>',
        phone: '0612345678',
        role: 'admin',
        permissions: ['all'],
        avatar: 'logo',
        lastLogin: null,
        isActive: true,
        createdAt: new Date().toISOString()
    },
    {
        id: 'teacher001',
        username: 'teacher',
        password: 'teacher123',
        name: 'أستاذ تجريبي',
        email: '<EMAIL>',
        phone: '0612345679',
        role: 'teacher',
        permissions: ['students_read', 'students_update', 'groups_read', 'payments_read'],
        avatar: 'logo',
        lastLogin: null,
        isActive: true,
        createdAt: new Date().toISOString()
    },
    {
        id: 'staff001',
        username: 'staff',
        password: 'staff123',
        name: 'موظف تجريبي',
        email: '<EMAIL>',
        phone: '0612345680',
        role: 'staff',
        permissions: ['students_read', 'payments_read', 'financial_read'],
        avatar: 'logo',
        lastLogin: null,
        isActive: true,
        createdAt: new Date().toISOString()
    }
];

// الصلاحيات المتاحة
const AVAILABLE_PERMISSIONS = {
    // إدارة التلاميذ
    'students_read': 'عرض بيانات التلاميذ',
    'students_create': 'إضافة تلاميذ جدد',
    'students_update': 'تعديل بيانات التلاميذ',
    'students_delete': 'حذف التلاميذ',
    
    // إدارة الأساتذة
    'teachers_read': 'عرض بيانات الأساتذة',
    'teachers_create': 'إضافة أساتذة جدد',
    'teachers_update': 'تعديل بيانات الأساتذة',
    'teachers_delete': 'حذف الأساتذة',
    
    // إدارة الموظفين
    'staff_read': 'عرض بيانات الموظفين',
    'staff_create': 'إضافة موظفين جدد',
    'staff_update': 'تعديل بيانات الموظفين',
    'staff_delete': 'حذف الموظفين',
    
    // إدارة المجموعات
    'groups_read': 'عرض المجموعات والمستويات',
    'groups_create': 'إنشاء مجموعات جديدة',
    'groups_update': 'تعديل المجموعات',
    'groups_delete': 'حذف المجموعات',
    
    // العمليات المالية
    'financial_read': 'عرض العمليات المالية',
    'financial_create': 'إنشاء عمليات مالية',
    'financial_update': 'تعديل العمليات المالية',
    'financial_delete': 'حذف العمليات المالية',
    
    // إدارة الدفعات
    'payments_read': 'عرض عمليات الدفع',
    'payments_create': 'تسجيل دفعات جديدة',
    'payments_update': 'تعديل عمليات الدفع',
    'payments_delete': 'حذف عمليات الدفع',
    
    // إدارة المستخدمين
    'users_read': 'عرض المستخدمين',
    'users_create': 'إضافة مستخدمين جدد',
    'users_update': 'تعديل بيانات المستخدمين',
    'users_delete': 'حذف المستخدمين',
    
    // الإعدادات
    'settings_read': 'عرض الإعدادات',
    'settings_update': 'تعديل الإعدادات',
    
    // التقارير
    'reports_read': 'عرض التقارير',
    'reports_export': 'تصدير التقارير',
    
    // WhatsApp
    'whatsapp_read': 'عرض رسائل WhatsApp',
    'whatsapp_send': 'إرسال رسائل WhatsApp',
    
    // الاستيراد والتصدير
    'import_data': 'استيراد البيانات',
    'export_data': 'تصدير البيانات',
    
    // صلاحية شاملة
    'all': 'جميع الصلاحيات'
};

// الأدوار وصلاحياتها الافتراضية
const ROLE_PERMISSIONS = {
    'admin': ['all'],
    'teacher': [
        'students_read', 'students_update',
        'groups_read',
        'payments_read',
        'reports_read',
        'whatsapp_read', 'whatsapp_send'
    ],
    'staff': [
        'students_read',
        'financial_read',
        'payments_read', 'payments_create',
        'reports_read'
    ]
};

// دالة تهيئة المستخدمين الافتراضيين المحسنة
function initializeDefaultUsers() {
    try {
        const existingUsers = JSON.parse(localStorage.getItem('sbea_users') || '[]');
        if (existingUsers.length === 0) {
            // إنشاء المستخدمين الافتراضيين مع معرفات فريدة
            const defaultUsers = DEFAULT_USERS.map(user => ({
                ...user,
                id: user.id || `${user.role}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
            }));

            localStorage.setItem('sbea_users', JSON.stringify(defaultUsers));
            console.log('تم إنشاء المستخدمين الافتراضيين:', defaultUsers.length);

            // إنشاء إعدادات افتراضية
            if (!localStorage.getItem('sbea_system_settings')) {
                const defaultSettings = {
                    schoolName: 'مؤسسة النور التربوي للتعليم الخصوصي',
                    currency: 'DHS',
                    academicYear: '2024-2025',
                    language: 'ar',
                    theme: 'light',
                    autoBackup: true,
                    backupInterval: 24, // ساعة
                    maxStudentsPerGroup: 30,
                    paymentMonths: ['سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
                };
                localStorage.setItem('sbea_system_settings', JSON.stringify(defaultSettings));
            }
        } else {
            console.log('المستخدمون موجودون بالفعل:', existingUsers.length);
        }
    } catch (error) {
        console.error('خطأ في تهيئة المستخدمين:', error);
        // إعادة تعيين في حالة وجود بيانات تالفة
        localStorage.removeItem('sbea_users');
        initializeDefaultUsers();
    }
}

// دالة تسجيل الدخول
async function login(username, password, userType) {
    try {
        const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
        const user = users.find(u => 
            u.username === username && 
            u.password === password && 
            u.role === userType &&
            u.isActive
        );

        if (!user) {
            throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
        }

        // تحديث آخر تسجيل دخول
        user.lastLogin = new Date().toISOString();
        const userIndex = users.findIndex(u => u.id === user.id);
        users[userIndex] = user;
        localStorage.setItem('sbea_users', JSON.stringify(users));

        // حفظ بيانات الجلسة
        const sessionData = {
            userId: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            permissions: user.permissions,
            loginTime: new Date().toISOString(),
            sessionId: generateSessionId()
        };

        localStorage.setItem('sbea_current_user', JSON.stringify(sessionData));
        sessionStorage.setItem('sbea_session_active', 'true');

        // تسجيل عملية تسجيل الدخول
        logUserActivity('login', `تسجيل دخول للمستخدم ${user.name}`);

        return { success: true, user: sessionData };
    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        return { success: false, error: error.message };
    }
}

// دالة تسجيل الخروج
function logout() {
    const currentUser = getCurrentUser();
    if (currentUser) {
        logUserActivity('logout', `تسجيل خروج للمستخدم ${currentUser.name}`);
    }

    localStorage.removeItem('sbea_current_user');
    sessionStorage.removeItem('sbea_session_active');
    
    // إعادة توجيه لصفحة تسجيل الدخول
    window.location.href = 'login.html';
}

// دالة الحصول على المستخدم الحالي
function getCurrentUser() {
    try {
        const userData = localStorage.getItem('sbea_current_user');
        const sessionActive = sessionStorage.getItem('sbea_session_active');
        
        if (!userData || !sessionActive) {
            return null;
        }

        return JSON.parse(userData);
    } catch (error) {
        console.error('خطأ في قراءة بيانات المستخدم:', error);
        return null;
    }
}

// دالة التحقق من الصلاحية
function hasPermission(permission) {
    const currentUser = getCurrentUser();
    if (!currentUser) return false;
    
    // المدير له جميع الصلاحيات
    if (currentUser.permissions.includes('all')) return true;
    
    return currentUser.permissions.includes(permission);
}

// دالة التحقق من تسجيل الدخول
function isLoggedIn() {
    const currentUser = getCurrentUser();
    const sessionActive = sessionStorage.getItem('sbea_session_active');
    return currentUser && sessionActive === 'true';
}

// دالة حماية الصفحات
function protectPage(requiredPermissions = []) {
    if (!isLoggedIn()) {
        window.location.href = 'login.html';
        return false;
    }

    if (requiredPermissions.length > 0) {
        const hasRequiredPermission = requiredPermissions.some(permission => 
            hasPermission(permission)
        );

        if (!hasRequiredPermission) {
            alert('ليس لديك صلاحية للوصول إلى هذه الصفحة');
            window.location.href = 'index.html';
            return false;
        }
    }

    return true;
}

// دالة إنشاء معرف الجلسة
function generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// دالة تسجيل أنشطة المستخدم
function logUserActivity(action, description) {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    const activities = JSON.parse(localStorage.getItem('sbea_user_activities') || '[]');
    const activity = {
        id: 'activity_' + Date.now(),
        userId: currentUser.userId,
        username: currentUser.username,
        name: currentUser.name,
        action: action,
        description: description,
        timestamp: new Date().toISOString(),
        ip: 'localhost', // في بيئة محلية
        userAgent: navigator.userAgent
    };

    activities.unshift(activity);
    
    // الاحتفاظ بآخر 1000 نشاط فقط
    if (activities.length > 1000) {
        activities.splice(1000);
    }

    localStorage.setItem('sbea_user_activities', JSON.stringify(activities));
}

// دالة تحديث معلومات المستخدم في الواجهة
function updateUserInterface() {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // تحديث اسم المستخدم في الواجهة
    const userNameElements = document.querySelectorAll('.current-user-name');
    userNameElements.forEach(element => {
        element.textContent = currentUser.name;
    });

    // تحديث دور المستخدم
    const userRoleElements = document.querySelectorAll('.current-user-role');
    userRoleElements.forEach(element => {
        const roleNames = {
            'admin': 'مدير',
            'teacher': 'أستاذ',
            'staff': 'موظف'
        };
        element.textContent = roleNames[currentUser.role] || currentUser.role;
    });

    // إخفاء/إظهار العناصر حسب الصلاحيات
    hideElementsByPermission();
}

// دالة إخفاء العناصر حسب الصلاحيات
function hideElementsByPermission() {
    const elementsWithPermissions = document.querySelectorAll('[data-permission]');
    
    elementsWithPermissions.forEach(element => {
        const requiredPermission = element.getAttribute('data-permission');
        if (!hasPermission(requiredPermission)) {
            element.style.display = 'none';
        }
    });
}

// معالج تسجيل الدخول المحسن
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة المستخدمين الافتراضيين
    initializeDefaultUsers();

    // التأكد من وجود حساب المدير
    ensureAdminExists();

    // إذا كانت صفحة تسجيل الدخول
    if (window.location.pathname.includes('login.html') || window.location.pathname.endsWith('/') || window.location.pathname.endsWith('index.html') && !isLoggedIn()) {
        // إذا لم نكن في صفحة تسجيل الدخول وغير مسجلين، إعادة توجيه
        if (!window.location.pathname.includes('login.html') && !isLoggedIn()) {
            window.location.href = 'login.html';
            return;
        }

        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', handleLogin);
        }

        // إذا كان المستخدم مسجل دخول بالفعل، إعادة توجيه للصفحة الرئيسية
        if (isLoggedIn() && window.location.pathname.includes('login.html')) {
            window.location.href = 'index.html';
        }
    } else {
        // حماية الصفحات الأخرى
        if (!protectPage()) {
            return;
        }

        // تحديث واجهة المستخدم
        setTimeout(() => {
            updateUserInterface();
        }, 100);
    }
});

// معالج نموذج تسجيل الدخول
async function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const userType = document.querySelector('input[name="userType"]:checked').value;
    
    const errorDiv = document.getElementById('error-message');
    const successDiv = document.getElementById('success-message');
    const loginBtn = document.getElementById('login-btn');
    const spinner = document.getElementById('loading-spinner');
    
    // إخفاء الرسائل السابقة
    errorDiv.style.display = 'none';
    successDiv.style.display = 'none';
    
    // تفعيل حالة التحميل
    loginBtn.disabled = true;
    spinner.style.display = 'inline-block';
    
    try {
        const result = await login(username, password, userType);
        
        if (result.success) {
            successDiv.textContent = 'تم تسجيل الدخول بنجاح! جاري التحويل...';
            successDiv.style.display = 'block';
            
            // تأخير قصير قبل التحويل
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        } else {
            errorDiv.textContent = result.error;
            errorDiv.style.display = 'block';
        }
    } catch (error) {
        errorDiv.textContent = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
        errorDiv.style.display = 'block';
    } finally {
        loginBtn.disabled = false;
        spinner.style.display = 'none';
    }
}

// دالة عرض نافذة نسيان كلمة المرور
function showForgotPassword() {
    alert('يرجى التواصل مع المدير لإعادة تعيين كلمة المرور');
}

// دالة تحديث معلومات المستخدم في الواجهة المحسنة
function updateUserInterface() {
    const currentUser = getCurrentUser();
    if (!currentUser) return;

    // تحديث اسم المستخدم في الواجهة
    const userNameElements = document.querySelectorAll('.current-user-name');
    userNameElements.forEach(element => {
        element.textContent = currentUser.name;
    });

    // تحديث دور المستخدم
    const userRoleElements = document.querySelectorAll('.current-user-role');
    userRoleElements.forEach(element => {
        const roleNames = {
            'admin': 'مدير',
            'teacher': 'أستاذ',
            'staff': 'موظف'
        };
        element.textContent = roleNames[currentUser.role] || currentUser.role;
    });

    // إخفاء/إظهار العناصر حسب الصلاحيات
    hideElementsByPermission();

    // إضافة أيقونة Font Awesome إذا لم تكن موجودة
    if (!document.querySelector('link[href*="font-awesome"]')) {
        const fontAwesome = document.createElement('link');
        fontAwesome.rel = 'stylesheet';
        fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
        document.head.appendChild(fontAwesome);
    }
}

// دالة إخفاء العناصر حسب الصلاحيات المحسنة
function hideElementsByPermission() {
    const elementsWithPermissions = document.querySelectorAll('[data-permission]');

    elementsWithPermissions.forEach(element => {
        const requiredPermission = element.getAttribute('data-permission');
        if (!hasPermission(requiredPermission)) {
            element.style.display = 'none';
        } else {
            element.style.display = '';
        }
    });
}

// دالة إنشاء حساب المدير الافتراضي إذا لم يكن موجوداً
function ensureAdminExists() {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const adminExists = users.some(user => user.role === 'admin');

    if (!adminExists) {
        const adminUser = {
            id: 'admin_' + Date.now(),
            username: 'admin',
            password: 'admin123',
            name: 'المدير العام',
            email: '<EMAIL>',
            phone: '0612345678',
            role: 'admin',
            permissions: ['all'],
            avatar: 'logo',
            lastLogin: null,
            isActive: true,
            createdAt: new Date().toISOString()
        };

        users.push(adminUser);
        localStorage.setItem('sbea_users', JSON.stringify(users));
        console.log('تم إنشاء حساب المدير الافتراضي');
    }
}

// دالة التحقق من صحة البيانات
function validateUserData(userData) {
    const errors = [];

    if (!userData.username || userData.username.length < 3) {
        errors.push('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
    }

    if (!userData.password || userData.password.length < 6) {
        errors.push('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    if (!userData.name || userData.name.length < 2) {
        errors.push('الاسم يجب أن يكون حرفين على الأقل');
    }

    if (!userData.email || !isValidEmail(userData.email)) {
        errors.push('البريد الإلكتروني غير صحيح');
    }

    if (!userData.role || !['admin', 'teacher', 'staff'].includes(userData.role)) {
        errors.push('الدور غير صحيح');
    }

    return errors;
}

// دالة التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// دالة تشفير كلمة المرور (بسيط)
function hashPassword(password) {
    // في بيئة الإنتاج، استخدم مكتبة تشفير قوية
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
        const char = password.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // تحويل إلى 32bit integer
    }
    return hash.toString();
}

// دالة تحديث كلمة المرور
function updatePassword(userId, newPassword) {
    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const userIndex = users.findIndex(u => u.id === userId);

    if (userIndex === -1) {
        return { success: false, error: 'المستخدم غير موجود' };
    }

    const validation = validateUserData({ ...users[userIndex], password: newPassword });
    if (validation.length > 0) {
        return { success: false, error: validation.join(', ') };
    }

    users[userIndex].password = newPassword; // في الإنتاج: hashPassword(newPassword)
    localStorage.setItem('sbea_users', JSON.stringify(users));

    logUserActivity('password_change', `تغيير كلمة مرور المستخدم ${users[userIndex].name}`);

    return { success: true };
}

// دالة تحديث ملف المستخدم الشخصي
function updateProfile(userData) {
    const currentUser = getCurrentUser();
    if (!currentUser) {
        return { success: false, error: 'المستخدم غير مسجل دخول' };
    }

    const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
    const userIndex = users.findIndex(u => u.id === currentUser.userId);

    if (userIndex === -1) {
        return { success: false, error: 'المستخدم غير موجود' };
    }

    // تحديث البيانات المسموحة فقط
    const allowedFields = ['name', 'email', 'phone', 'avatar'];
    allowedFields.forEach(field => {
        if (userData[field] !== undefined) {
            users[userIndex][field] = userData[field];
        }
    });

    localStorage.setItem('sbea_users', JSON.stringify(users));

    // تحديث بيانات الجلسة
    const sessionData = JSON.parse(localStorage.getItem('sbea_current_user'));
    sessionData.name = users[userIndex].name;
    localStorage.setItem('sbea_current_user', JSON.stringify(sessionData));

    logUserActivity('profile_update', 'تحديث الملف الشخصي');

    return { success: true };
}

// تصدير الدوال للاستخدام في ملفات أخرى
window.authSystem = {
    login,
    logout,
    getCurrentUser,
    hasPermission,
    isLoggedIn,
    protectPage,
    logUserActivity,
    updateUserInterface,
    hideElementsByPermission,
    ensureAdminExists,
    validateUserData,
    updatePassword,
    updateProfile,
    AVAILABLE_PERMISSIONS,
    ROLE_PERMISSIONS
};

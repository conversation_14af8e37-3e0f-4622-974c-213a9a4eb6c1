<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النفقات والمصاريف - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="expenses.html" class="active">إدارة النفقات</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="profile.html">الملف الشخصي</a></li>
                <li><a href="diagnostics.html" data-permission="all">تشخيص النظام</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="expenses-management">
            <h2>💰 إدارة النفقات والمصاريف اليومية</h2>
            
            <!-- تبويبات إدارة النفقات -->
            <div class="expenses-tabs">
                <button class="expenses-tab-btn active" data-tab="daily-expenses">النفقات اليومية</button>
                <button class="expenses-tab-btn" data-tab="categories">فئات النفقات</button>
                <button class="expenses-tab-btn" data-tab="reports">التقارير المالية</button>
                <button class="expenses-tab-btn" data-tab="budget">الميزانية</button>
            </div>

            <!-- النفقات اليومية -->
            <div id="daily-expenses-tab" class="expenses-tab-content active">
                <h3>📝 تسجيل النفقات اليومية</h3>
                
                <!-- نموذج إضافة نفقة جديدة -->
                <div class="expense-form-container">
                    <form id="add-expense-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expense-date">التاريخ</label>
                                <input type="date" id="expense-date" required>
                            </div>
                            <div class="form-group">
                                <label for="expense-category">فئة النفقة</label>
                                <select id="expense-category" required>
                                    <option value="">اختر الفئة</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="expense-amount">المبلغ (درهم)</label>
                                <input type="number" id="expense-amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expense-description">وصف النفقة</label>
                                <input type="text" id="expense-description" placeholder="وصف مفصل للنفقة" required>
                            </div>
                            <div class="form-group">
                                <label for="expense-payment-method">طريقة الدفع</label>
                                <select id="expense-payment-method" required>
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash">نقداً</option>
                                    <option value="bank">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="card">بطاقة ائتمان</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="expense-receipt">رقم الإيصال/الفاتورة</label>
                                <input type="text" id="expense-receipt" placeholder="رقم الإيصال (اختياري)">
                            </div>
                            <div class="form-group">
                                <label for="expense-notes">ملاحظات</label>
                                <textarea id="expense-notes" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                        
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-plus"></i> إضافة النفقة
                        </button>
                    </form>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="expenses-summary">
                    <div class="summary-card total">
                        <div class="summary-icon">💰</div>
                        <div class="summary-content">
                            <h4>إجمالي النفقات اليوم</h4>
                            <p id="today-expenses-total">0.00 DHS</p>
                        </div>
                    </div>
                    <div class="summary-card monthly">
                        <div class="summary-icon">📅</div>
                        <div class="summary-content">
                            <h4>إجمالي النفقات هذا الشهر</h4>
                            <p id="month-expenses-total">0.00 DHS</p>
                        </div>
                    </div>
                    <div class="summary-card yearly">
                        <div class="summary-icon">📊</div>
                        <div class="summary-content">
                            <h4>إجمالي النفقات هذا العام</h4>
                            <p id="year-expenses-total">0.00 DHS</p>
                        </div>
                    </div>
                    <div class="summary-card count">
                        <div class="summary-icon">📝</div>
                        <div class="summary-content">
                            <h4>عدد النفقات اليوم</h4>
                            <p id="today-expenses-count">0</p>
                        </div>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="expenses-filters">
                    <div class="filter-group">
                        <label for="filter-date-from">من تاريخ</label>
                        <input type="date" id="filter-date-from">
                    </div>
                    <div class="filter-group">
                        <label for="filter-date-to">إلى تاريخ</label>
                        <input type="date" id="filter-date-to">
                    </div>
                    <div class="filter-group">
                        <label for="filter-category">الفئة</label>
                        <select id="filter-category">
                            <option value="">جميع الفئات</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="filter-payment-method">طريقة الدفع</label>
                        <select id="filter-payment-method">
                            <option value="">جميع الطرق</option>
                            <option value="cash">نقداً</option>
                            <option value="bank">تحويل بنكي</option>
                            <option value="check">شيك</option>
                            <option value="card">بطاقة ائتمان</option>
                        </select>
                    </div>
                    <button id="apply-filters-btn" class="filter-btn">
                        <i class="fas fa-filter"></i> تطبيق الفلاتر
                    </button>
                    <button id="clear-filters-btn" class="filter-btn secondary">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                </div>

                <!-- قائمة النفقات -->
                <div class="expenses-list-container">
                    <div class="expenses-actions">
                        <button id="export-expenses-btn" class="action-btn">
                            <i class="fas fa-download"></i> تصدير النفقات
                        </button>
                        <button id="print-expenses-btn" class="action-btn">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                    </div>
                    
                    <div id="expenses-list" class="expenses-table-container">
                        <!-- سيتم ملء قائمة النفقات هنا بواسطة JavaScript -->
                    </div>
                </div>
            </div>

            <!-- فئات النفقات -->
            <div id="categories-tab" class="expenses-tab-content">
                <h3>🏷️ إدارة فئات النفقات</h3>
                
                <div class="categories-management">
                    <form id="add-category-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="category-name">اسم الفئة</label>
                                <input type="text" id="category-name" placeholder="مثال: مواد تعليمية" required>
                            </div>
                            <div class="form-group">
                                <label for="category-description">الوصف</label>
                                <input type="text" id="category-description" placeholder="وصف الفئة">
                            </div>
                            <div class="form-group">
                                <label for="category-color">اللون</label>
                                <input type="color" id="category-color" value="#3498db">
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-plus"></i> إضافة فئة
                        </button>
                    </form>
                    
                    <div id="categories-list" class="categories-grid">
                        <!-- سيتم ملء قائمة الفئات هنا -->
                    </div>
                </div>
            </div>

            <!-- التقارير المالية -->
            <div id="reports-tab" class="expenses-tab-content">
                <h3>📊 التقارير المالية للنفقات</h3>
                
                <div class="reports-controls">
                    <div class="report-filters">
                        <select id="report-period">
                            <option value="daily">يومي</option>
                            <option value="weekly">أسبوعي</option>
                            <option value="monthly" selected>شهري</option>
                            <option value="yearly">سنوي</option>
                            <option value="custom">فترة مخصصة</option>
                        </select>
                        
                        <div id="custom-period" style="display: none;">
                            <input type="date" id="report-start-date">
                            <input type="date" id="report-end-date">
                        </div>
                        
                        <button id="generate-report-btn" class="report-btn">
                            <i class="fas fa-chart-bar"></i> إنشاء التقرير
                        </button>
                    </div>
                </div>
                
                <div id="expenses-report-content">
                    <!-- سيتم ملء محتوى التقرير هنا -->
                </div>
            </div>

            <!-- الميزانية -->
            <div id="budget-tab" class="expenses-tab-content">
                <h3>💼 إدارة الميزانية</h3>
                
                <div class="budget-management">
                    <form id="budget-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="budget-month">الشهر</label>
                                <select id="budget-month" required>
                                    <option value="">اختر الشهر</option>
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="budget-year">السنة</label>
                                <input type="number" id="budget-year" min="2020" max="2030" required>
                            </div>
                            <div class="form-group">
                                <label for="budget-amount">المبلغ المخصص</label>
                                <input type="number" id="budget-amount" step="0.01" min="0" required>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">
                            <i class="fas fa-save"></i> حفظ الميزانية
                        </button>
                    </form>
                    
                    <div id="budget-overview" class="budget-cards">
                        <!-- سيتم ملء نظرة عامة على الميزانية هنا -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- نافذة تعديل النفقة -->
    <div id="edit-expense-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✏️ تعديل النفقة</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <form id="edit-expense-form">
                    <!-- نفس حقول نموذج الإضافة -->
                    <input type="hidden" id="edit-expense-id">
                    <!-- باقي الحقول ستتم إضافتها بواسطة JavaScript -->
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="delete-expense-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🗑️ تأكيد الحذف</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه النفقة؟</p>
                <div class="modal-actions">
                    <button id="confirm-delete-btn" class="danger-btn">حذف</button>
                    <button class="cancel-btn">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <!-- تضمين ملفات JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="auth.js"></script>
    <script src="loading.js"></script>
    <script src="notifications.js"></script>
    <script src="validation.js"></script>
    <script src="animations.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
</body>
</html>

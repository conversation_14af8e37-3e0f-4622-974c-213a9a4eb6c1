<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - نظام إدارة مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: '<PERSON><PERSON>wal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #007bff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 1.2rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .test-card:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,123,255,0.2);
        }

        .test-card.success {
            border-color: #28a745;
            background: #d4edda;
        }

        .test-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .test-card.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }

        .test-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .test-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .test-description {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .test-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
            display: inline-block;
        }

        .status-success {
            background: #28a745;
            color: white;
        }

        .status-error {
            background: #dc3545;
            color: white;
        }

        .status-warning {
            background: #ffc107;
            color: #333;
        }

        .status-info {
            background: #17a2b8;
            color: white;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #333;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .system-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: right;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .test-container {
                padding: 20px;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="logo">🏫</div>
        <h1>اختبار سريع للنظام</h1>
        <p class="subtitle">فحص شامل لجميع مكونات نظام إدارة مؤسسة النور التربوي</p>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري فحص النظام...</p>
        </div>

        <div class="test-grid" id="test-results">
            <!-- سيتم ملء النتائج هنا -->
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="runTests()">🔍 تشغيل الفحص</button>
            <a href="index.html" class="btn btn-success">🏠 الصفحة الرئيسية</a>
            <button class="btn btn-warning" onclick="runMaintenance()">🔧 صيانة النظام</button>
            <button class="btn btn-info" onclick="exportReport()">📊 تصدير التقرير</button>
        </div>

        <div class="system-info" id="system-info">
            <h3 style="margin-bottom: 15px;">معلومات النظام</h3>
            <div class="info-row">
                <strong>المتصفح:</strong>
                <span id="browser-info">جاري التحميل...</span>
            </div>
            <div class="info-row">
                <strong>الإصدار:</strong>
                <span>2.0</span>
            </div>
            <div class="info-row">
                <strong>تاريخ آخر تحديث:</strong>
                <span>2025-01-10</span>
            </div>
            <div class="info-row">
                <strong>حالة النظام:</strong>
                <span id="system-status">جاري الفحص...</span>
            </div>
        </div>
    </div>

    <script>
        // تحديث معلومات المتصفح
        document.getElementById('browser-info').textContent = navigator.userAgent.split(' ')[0];

        // اختبارات النظام
        const tests = [
            {
                id: 'localStorage',
                title: 'التخزين المحلي',
                description: 'فحص توفر وعمل التخزين المحلي',
                icon: '💾',
                test: () => {
                    try {
                        localStorage.setItem('test', 'test');
                        localStorage.removeItem('test');
                        return { status: 'success', message: 'يعمل بشكل صحيح' };
                    } catch (e) {
                        return { status: 'error', message: 'غير متوفر أو معطل' };
                    }
                }
            },
            {
                id: 'javascript',
                title: 'JavaScript',
                description: 'فحص تمكين وعمل JavaScript',
                icon: '⚡',
                test: () => {
                    return { status: 'success', message: 'مفعل ويعمل' };
                }
            },
            {
                id: 'data',
                title: 'قاعدة البيانات',
                description: 'فحص سلامة البيانات المحفوظة',
                icon: '🗄️',
                test: () => {
                    try {
                        const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                        const teachers = JSON.parse(localStorage.getItem('sbea_teachers') || '[]');
                        return { 
                            status: 'success', 
                            message: `${students.length} تلميذ، ${teachers.length} أستاذ` 
                        };
                    } catch (e) {
                        return { status: 'error', message: 'بيانات تالفة' };
                    }
                }
            },
            {
                id: 'features',
                title: 'الميزات الجديدة',
                description: 'فحص الميزات المضافة حديثاً',
                icon: '🆕',
                test: () => {
                    const features = [
                        'sbea_expenses',
                        'sbea_grades',
                        'sbea_notes',
                        'sbea_auto_logs'
                    ];
                    
                    let working = 0;
                    features.forEach(feature => {
                        if (localStorage.getItem(feature) !== null) working++;
                    });
                    
                    if (working === features.length) {
                        return { status: 'success', message: 'جميع الميزات متوفرة' };
                    } else if (working > 0) {
                        return { status: 'warning', message: `${working}/${features.length} متوفرة` };
                    } else {
                        return { status: 'info', message: 'لم يتم الاستخدام بعد' };
                    }
                }
            },
            {
                id: 'performance',
                title: 'الأداء',
                description: 'فحص أداء النظام واستخدام الذاكرة',
                icon: '⚡',
                test: () => {
                    if (performance.memory) {
                        const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                        const limit = Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024);
                        
                        if (used < limit * 0.5) {
                            return { status: 'success', message: `${used}MB/${limit}MB` };
                        } else if (used < limit * 0.8) {
                            return { status: 'warning', message: `${used}MB/${limit}MB` };
                        } else {
                            return { status: 'error', message: `${used}MB/${limit}MB` };
                        }
                    }
                    return { status: 'info', message: 'غير متوفر' };
                }
            },
            {
                id: 'connectivity',
                title: 'الاتصال',
                description: 'فحص حالة الاتصال بالإنترنت',
                icon: '🌐',
                test: () => {
                    if (navigator.onLine) {
                        return { status: 'success', message: 'متصل بالإنترنت' };
                    } else {
                        return { status: 'warning', message: 'غير متصل' };
                    }
                }
            }
        ];

        function runTests() {
            const loading = document.getElementById('loading');
            const results = document.getElementById('test-results');
            
            loading.style.display = 'block';
            results.innerHTML = '';
            
            setTimeout(() => {
                loading.style.display = 'none';
                
                let overallStatus = 'success';
                let successCount = 0;
                let errorCount = 0;
                let warningCount = 0;
                
                tests.forEach(test => {
                    const result = test.test();
                    
                    if (result.status === 'success') successCount++;
                    else if (result.status === 'error') {
                        errorCount++;
                        overallStatus = 'error';
                    } else if (result.status === 'warning') {
                        warningCount++;
                        if (overallStatus !== 'error') overallStatus = 'warning';
                    }
                    
                    const card = document.createElement('div');
                    card.className = `test-card ${result.status}`;
                    card.innerHTML = `
                        <div class="test-icon">${test.icon}</div>
                        <div class="test-title">${test.title}</div>
                        <div class="test-description">${test.description}</div>
                        <div class="test-status status-${result.status}">${result.message}</div>
                    `;
                    
                    results.appendChild(card);
                });
                
                // تحديث حالة النظام
                const statusEl = document.getElementById('system-status');
                if (overallStatus === 'success') {
                    statusEl.innerHTML = '<span style="color: #28a745;">✅ ممتاز</span>';
                } else if (overallStatus === 'warning') {
                    statusEl.innerHTML = '<span style="color: #ffc107;">⚠️ جيد مع تحذيرات</span>';
                } else {
                    statusEl.innerHTML = '<span style="color: #dc3545;">❌ يحتاج مراجعة</span>';
                }
                
                console.log(`نتائج الفحص: ${successCount} نجح، ${warningCount} تحذير، ${errorCount} خطأ`);
            }, 2000);
        }

        function runMaintenance() {
            if (confirm('هل تريد تشغيل صيانة شاملة للنظام؟')) {
                console.log('🔧 بدء صيانة النظام...');
                
                // تنظيف البيانات التالفة
                try {
                    const keys = Object.keys(localStorage).filter(key => key.startsWith('sbea_'));
                    keys.forEach(key => {
                        try {
                            JSON.parse(localStorage.getItem(key));
                        } catch (e) {
                            localStorage.removeItem(key);
                            console.log(`🗑️ تم حذف البيانات التالفة: ${key}`);
                        }
                    });
                    
                    alert('✅ تمت الصيانة بنجاح!');
                    runTests(); // إعادة تشغيل الفحص
                } catch (error) {
                    alert('❌ حدث خطأ أثناء الصيانة');
                    console.error(error);
                }
            }
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                browser: navigator.userAgent,
                tests: tests.map(test => ({
                    name: test.title,
                    result: test.test()
                })),
                systemInfo: {
                    version: '2.0',
                    lastUpdate: '2025-01-10'
                }
            };
            
            const dataStr = JSON.stringify(report, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `تقرير_النظام_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // تشغيل الفحص تلقائياً عند التحميل
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>

// ===================================================================
//                    نظام الإشعارات والتنبيهات المتقدم
// ===================================================================

class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.defaultDuration = 5000;
        this.maxNotifications = 5;
        this.init();
    }

    // تهيئة النظام
    init() {
        this.createContainer();
        this.setupStyles();
    }

    // إنشاء حاوية الإشعارات
    createContainer() {
        this.container = document.createElement('div');
        this.container.id = 'notifications-container';
        this.container.className = 'notifications-container';
        document.body.appendChild(this.container);
    }

    // إعداد الأنماط
    setupStyles() {
        if (document.getElementById('notification-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'notification-styles';
        styles.textContent = `
            .notifications-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            }

            .notification {
                background: white;
                border-radius: 12px;
                padding: 16px 20px;
                margin-bottom: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                border-left: 4px solid #007bff;
                min-width: 320px;
                max-width: 400px;
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
                position: relative;
                overflow: hidden;
            }

            .notification.show {
                transform: translateX(0);
                opacity: 1;
            }

            .notification.hide {
                transform: translateX(100%);
                opacity: 0;
                margin-bottom: 0;
                padding-top: 0;
                padding-bottom: 0;
                max-height: 0;
            }

            .notification.success {
                border-left-color: #28a745;
            }

            .notification.error {
                border-left-color: #dc3545;
            }

            .notification.warning {
                border-left-color: #ffc107;
            }

            .notification.info {
                border-left-color: #17a2b8;
            }

            .notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
            }

            .notification-title {
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: bold;
                color: #333;
                font-size: 14px;
            }

            .notification-icon {
                font-size: 16px;
            }

            .notification.success .notification-icon {
                color: #28a745;
            }

            .notification.error .notification-icon {
                color: #dc3545;
            }

            .notification.warning .notification-icon {
                color: #ffc107;
            }

            .notification.info .notification-icon {
                color: #17a2b8;
            }

            .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #999;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s ease;
            }

            .notification-close:hover {
                background: #f8f9fa;
                color: #666;
            }

            .notification-message {
                color: #666;
                font-size: 13px;
                line-height: 1.4;
                margin: 0;
            }

            .notification-actions {
                margin-top: 12px;
                display: flex;
                gap: 8px;
                justify-content: flex-end;
            }

            .notification-action {
                background: #007bff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .notification-action:hover {
                background: #0056b3;
            }

            .notification-action.secondary {
                background: #6c757d;
            }

            .notification-action.secondary:hover {
                background: #545b62;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(0,123,255,0.3);
                transition: width linear;
            }

            .notification.success .notification-progress {
                background: rgba(40,167,69,0.3);
            }

            .notification.error .notification-progress {
                background: rgba(220,53,69,0.3);
            }

            .notification.warning .notification-progress {
                background: rgba(255,193,7,0.3);
            }

            .notification.info .notification-progress {
                background: rgba(23,162,184,0.3);
            }

            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            .notification.shake {
                animation: shake 0.5s ease-in-out;
            }

            @media (max-width: 768px) {
                .notifications-container {
                    right: 10px;
                    left: 10px;
                    top: 10px;
                }

                .notification {
                    min-width: auto;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(styles);
    }

    // عرض إشعار
    show(options) {
        const notification = this.createNotification(options);
        this.addNotification(notification);
        return notification.id;
    }

    // إنشاء إشعار
    createNotification(options) {
        const {
            type = 'info',
            title = '',
            message = '',
            duration = this.defaultDuration,
            actions = [],
            persistent = false,
            onClick = null
        } = options;

        const id = 'notification_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
        
        const notification = {
            id,
            type,
            title,
            message,
            duration,
            actions,
            persistent,
            onClick,
            element: null,
            timer: null
        };

        notification.element = this.createElement(notification);
        return notification;
    }

    // إنشاء عنصر HTML للإشعار
    createElement(notification) {
        const element = document.createElement('div');
        element.className = `notification ${notification.type}`;
        element.setAttribute('data-id', notification.id);

        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };

        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };

        element.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    <i class="notification-icon ${icons[notification.type]}"></i>
                    <span>${notification.title || titles[notification.type]}</span>
                </div>
                <button class="notification-close" onclick="window.notificationSystem.close('${notification.id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <p class="notification-message">${notification.message}</p>
            ${notification.actions.length > 0 ? `
                <div class="notification-actions">
                    ${notification.actions.map((action, index) => `
                        <button class="notification-action ${action.type || ''}" 
                                onclick="window.notificationSystem.handleAction('${notification.id}', ${index})">
                            ${action.text}
                        </button>
                    `).join('')}
                </div>
            ` : ''}
            ${!notification.persistent ? '<div class="notification-progress"></div>' : ''}
        `;

        // إضافة مستمع النقر
        if (notification.onClick) {
            element.addEventListener('click', notification.onClick);
            element.style.cursor = 'pointer';
        }

        return element;
    }

    // إضافة إشعار للحاوية
    addNotification(notification) {
        // إزالة الإشعارات الزائدة
        while (this.notifications.length >= this.maxNotifications) {
            this.close(this.notifications[0].id);
        }

        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // تأثير الظهور
        setTimeout(() => {
            notification.element.classList.add('show');
        }, 10);

        // تشغيل شريط التقدم
        if (!notification.persistent) {
            this.startProgress(notification);
            
            // إزالة تلقائية
            notification.timer = setTimeout(() => {
                this.close(notification.id);
            }, notification.duration);
        }
    }

    // تشغيل شريط التقدم
    startProgress(notification) {
        const progressBar = notification.element.querySelector('.notification-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${notification.duration}ms linear`;
            
            setTimeout(() => {
                progressBar.style.width = '0%';
            }, 10);
        }
    }

    // إغلاق إشعار
    close(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (!notification) return;

        // إيقاف المؤقت
        if (notification.timer) {
            clearTimeout(notification.timer);
        }

        // تأثير الاختفاء
        notification.element.classList.add('hide');
        
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            
            // إزالة من المصفوفة
            const index = this.notifications.findIndex(n => n.id === id);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 400);
    }

    // معالجة إجراءات الإشعار
    handleAction(notificationId, actionIndex) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification || !notification.actions[actionIndex]) return;

        const action = notification.actions[actionIndex];
        if (action.callback) {
            action.callback();
        }

        // إغلاق الإشعار بعد تنفيذ الإجراء
        if (action.closeAfter !== false) {
            this.close(notificationId);
        }
    }

    // إشعارات سريعة
    success(message, title = 'تم بنجاح') {
        return this.show({
            type: 'success',
            title,
            message,
            duration: 4000
        });
    }

    error(message, title = 'حدث خطأ') {
        return this.show({
            type: 'error',
            title,
            message,
            duration: 6000
        });
    }

    warning(message, title = 'تحذير') {
        return this.show({
            type: 'warning',
            title,
            message,
            duration: 5000
        });
    }

    info(message, title = 'معلومات') {
        return this.show({
            type: 'info',
            title,
            message,
            duration: 4000
        });
    }

    // إشعار تأكيد
    confirm(message, onConfirm, onCancel = null) {
        return this.show({
            type: 'warning',
            title: 'تأكيد',
            message,
            persistent: true,
            actions: [
                {
                    text: 'تأكيد',
                    type: 'primary',
                    callback: onConfirm
                },
                {
                    text: 'إلغاء',
                    type: 'secondary',
                    callback: onCancel
                }
            ]
        });
    }

    // إشعار التحميل
    loading(message = 'جاري التحميل...') {
        return this.show({
            type: 'info',
            title: 'يرجى الانتظار',
            message,
            persistent: true
        });
    }

    // إغلاق جميع الإشعارات
    closeAll() {
        const notificationIds = this.notifications.map(n => n.id);
        notificationIds.forEach(id => this.close(id));
    }

    // إشعار التحديث
    updateNotification(message = 'تم تحديث البيانات') {
        return this.info(message, 'تحديث');
    }

    // إشعار الحفظ
    saveNotification(message = 'تم حفظ البيانات بنجاح') {
        return this.success(message, 'تم الحفظ');
    }

    // إشعار الحذف
    deleteNotification(message = 'تم حذف العنصر بنجاح') {
        return this.success(message, 'تم الحذف');
    }
}

// إنشاء مثيل عام للنظام
window.notificationSystem = new NotificationSystem();

// اختصارات سريعة
window.showSuccess = (message, title) => window.notificationSystem.success(message, title);
window.showError = (message, title) => window.notificationSystem.error(message, title);
window.showWarning = (message, title) => window.notificationSystem.warning(message, title);
window.showInfo = (message, title) => window.notificationSystem.info(message, title);
window.showConfirm = (message, onConfirm, onCancel) => window.notificationSystem.confirm(message, onConfirm, onCancel);

// تصدير النظام
export { NotificationSystem };

// ===================================================================
//                    نظام التسجيل التلقائي الشامل
// ===================================================================

class AutoLoggingSystem {
    constructor() {
        this.logs = [];
        this.maxLogs = 1000; // الحد الأقصى للسجلات
        this.isEnabled = true;
        this.logTypes = {
            CREATE: 'إنشاء',
            UPDATE: 'تحديث', 
            DELETE: 'حذف',
            LOGIN: 'تسجيل دخول',
            LOGOUT: 'تسجيل خروج',
            PAYMENT: 'دفعة',
            EXPORT: 'تصدير',
            IMPORT: 'استيراد',
            PRINT: 'طباعة',
            VIEW: 'عرض',
            SEARCH: 'بحث',
            ERROR: 'خطأ',
            WARNING: 'تحذير',
            INFO: 'معلومات'
        };
        this.init();
    }

    // تهيئة النظام
    init() {
        this.loadLogs();
        this.setupAutoLogging();
        this.setupPeriodicCleanup();
        this.interceptFormSubmissions();
        this.interceptButtonClicks();
        this.interceptPageNavigation();
    }

    // تحميل السجلات من التخزين المحلي
    loadLogs() {
        try {
            const savedLogs = localStorage.getItem('sbea_auto_logs');
            this.logs = savedLogs ? JSON.parse(savedLogs) : [];
        } catch (error) {
            console.warn('خطأ في تحميل سجلات النظام:', error);
            this.logs = [];
        }
    }

    // حفظ السجلات في التخزين المحلي
    saveLogs() {
        try {
            // الاحتفاظ بآخر 1000 سجل فقط
            if (this.logs.length > this.maxLogs) {
                this.logs = this.logs.slice(-this.maxLogs);
            }
            localStorage.setItem('sbea_auto_logs', JSON.stringify(this.logs));
        } catch (error) {
            console.warn('خطأ في حفظ سجلات النظام:', error);
        }
    }

    // إضافة سجل جديد
    log(type, action, details = {}, userId = null) {
        if (!this.isEnabled) return;

        const logEntry = {
            id: this.generateLogId(),
            type: type,
            action: action,
            details: details,
            userId: userId || this.getCurrentUserId(),
            userName: this.getCurrentUserName(),
            timestamp: new Date().toISOString(),
            page: window.location.pathname.split('/').pop(),
            userAgent: navigator.userAgent,
            sessionId: this.getSessionId()
        };

        this.logs.push(logEntry);
        this.saveLogs();

        // إرسال إشعار للنوافذ الأخرى
        this.broadcastLog(logEntry);

        // طباعة في وحدة التحكم للتطوير
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🔍 سجل تلقائي:', logEntry);
        }
    }

    // توليد معرف فريد للسجل
    generateLogId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // الحصول على معرف المستخدم الحالي
    getCurrentUserId() {
        if (window.authSystem && window.authSystem.getCurrentUser) {
            return window.authSystem.getCurrentUser()?.id || 'unknown';
        }
        return 'guest';
    }

    // الحصول على اسم المستخدم الحالي
    getCurrentUserName() {
        if (window.authSystem && window.authSystem.getCurrentUser) {
            return window.authSystem.getCurrentUser()?.username || 'مجهول';
        }
        return 'ضيف';
    }

    // الحصول على معرف الجلسة
    getSessionId() {
        let sessionId = sessionStorage.getItem('sbea_session_id');
        if (!sessionId) {
            sessionId = this.generateLogId();
            sessionStorage.setItem('sbea_session_id', sessionId);
        }
        return sessionId;
    }

    // إعداد التسجيل التلقائي
    setupAutoLogging() {
        // تسجيل تحميل الصفحة
        this.log(this.logTypes.VIEW, 'تحميل الصفحة', {
            page: document.title,
            url: window.location.href
        });

        // تسجيل مغادرة الصفحة
        window.addEventListener('beforeunload', () => {
            this.log(this.logTypes.VIEW, 'مغادرة الصفحة', {
                page: document.title,
                timeSpent: Date.now() - this.pageLoadTime
            });
        });

        this.pageLoadTime = Date.now();
    }

    // اعتراض إرسال النماذج
    interceptFormSubmissions() {
        document.addEventListener('submit', (event) => {
            const form = event.target;
            const formId = form.id || form.className || 'نموذج غير محدد';
            
            // جمع بيانات النموذج (بدون كلمات المرور)
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                if (!key.toLowerCase().includes('password') && 
                    !key.toLowerCase().includes('كلمة') &&
                    !key.toLowerCase().includes('سر')) {
                    data[key] = value;
                }
            }

            this.log(this.logTypes.CREATE, 'إرسال نموذج', {
                formId: formId,
                formData: data,
                formAction: form.action || 'غير محدد'
            });
        });
    }

    // اعتراض النقر على الأزرار
    interceptButtonClicks() {
        document.addEventListener('click', (event) => {
            const target = event.target;
            
            // تسجيل النقر على الأزرار المهمة
            if (target.tagName === 'BUTTON' || target.classList.contains('btn')) {
                const buttonText = target.textContent.trim() || target.title || 'زر غير محدد';
                const buttonId = target.id || target.className;
                
                this.log(this.logTypes.INFO, 'نقر على زر', {
                    buttonText: buttonText,
                    buttonId: buttonId,
                    elementType: target.tagName
                });
            }

            // تسجيل النقر على الروابط
            if (target.tagName === 'A') {
                const linkText = target.textContent.trim();
                const linkHref = target.href;
                
                this.log(this.logTypes.VIEW, 'نقر على رابط', {
                    linkText: linkText,
                    linkHref: linkHref
                });
            }
        });
    }

    // اعتراض التنقل بين الصفحات
    interceptPageNavigation() {
        // تسجيل تغيير الصفحة
        let currentPage = window.location.href;
        
        setInterval(() => {
            if (window.location.href !== currentPage) {
                this.log(this.logTypes.VIEW, 'تغيير الصفحة', {
                    fromPage: currentPage,
                    toPage: window.location.href
                });
                currentPage = window.location.href;
            }
        }, 1000);
    }

    // إعداد التنظيف الدوري
    setupPeriodicCleanup() {
        // تنظيف السجلات القديمة كل ساعة
        setInterval(() => {
            this.cleanupOldLogs();
        }, 60 * 60 * 1000); // ساعة واحدة
    }

    // تنظيف السجلات القديمة
    cleanupOldLogs() {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        
        const initialCount = this.logs.length;
        this.logs = this.logs.filter(log => new Date(log.timestamp) > oneWeekAgo);
        
        if (this.logs.length < initialCount) {
            this.saveLogs();
            this.log(this.logTypes.INFO, 'تنظيف السجلات القديمة', {
                removedCount: initialCount - this.logs.length,
                remainingCount: this.logs.length
            });
        }
    }

    // بث السجل للنوافذ الأخرى
    broadcastLog(logEntry) {
        try {
            const event = new CustomEvent('autoLogAdded', {
                detail: logEntry
            });
            window.dispatchEvent(event);
            
            // إرسال للنوافذ الأخرى عبر localStorage
            const broadcastData = {
                type: 'AUTO_LOG',
                data: logEntry,
                timestamp: Date.now()
            };
            localStorage.setItem('sbea_log_broadcast', JSON.stringify(broadcastData));
            localStorage.removeItem('sbea_log_broadcast');
        } catch (error) {
            console.warn('خطأ في بث السجل:', error);
        }
    }

    // الحصول على السجلات مع الفلترة
    getLogs(filters = {}) {
        let filteredLogs = [...this.logs];

        // فلترة حسب النوع
        if (filters.type) {
            filteredLogs = filteredLogs.filter(log => log.type === filters.type);
        }

        // فلترة حسب المستخدم
        if (filters.userId) {
            filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
        }

        // فلترة حسب التاريخ
        if (filters.startDate) {
            const startDate = new Date(filters.startDate);
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
        }

        if (filters.endDate) {
            const endDate = new Date(filters.endDate);
            filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
        }

        // فلترة حسب الصفحة
        if (filters.page) {
            filteredLogs = filteredLogs.filter(log => log.page === filters.page);
        }

        // ترتيب حسب التاريخ (الأحدث أولاً)
        filteredLogs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        return filteredLogs;
    }

    // تصدير السجلات
    exportLogs(format = 'json') {
        const logs = this.getLogs();
        const timestamp = new Date().toISOString().split('T')[0];
        
        if (format === 'json') {
            const dataStr = JSON.stringify(logs, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `سجلات_النظام_${timestamp}.json`;
            link.click();
        } else if (format === 'csv') {
            const csvContent = this.convertLogsToCSV(logs);
            const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `سجلات_النظام_${timestamp}.csv`;
            link.click();
        }

        this.log(this.logTypes.EXPORT, 'تصدير السجلات', {
            format: format,
            recordCount: logs.length
        });
    }

    // تحويل السجلات إلى CSV
    convertLogsToCSV(logs) {
        const headers = ['التاريخ', 'النوع', 'الإجراء', 'المستخدم', 'الصفحة', 'التفاصيل'];
        const csvRows = [headers.join(',')];
        
        logs.forEach(log => {
            const row = [
                new Date(log.timestamp).toLocaleString('ar-MA'),
                log.type,
                log.action,
                log.userName,
                log.page,
                JSON.stringify(log.details).replace(/"/g, '""')
            ];
            csvRows.push(row.join(','));
        });
        
        return csvRows.join('\n');
    }

    // تمكين/تعطيل النظام
    setEnabled(enabled) {
        this.isEnabled = enabled;
        this.log(this.logTypes.INFO, enabled ? 'تمكين نظام التسجيل' : 'تعطيل نظام التسجيل');
    }

    // مسح جميع السجلات
    clearLogs() {
        const logCount = this.logs.length;
        this.logs = [];
        this.saveLogs();
        
        // تسجيل عملية المسح
        this.log(this.logTypes.WARNING, 'مسح جميع السجلات', {
            clearedCount: logCount
        });
    }

    // الحصول على إحصائيات السجلات
    getStatistics() {
        const stats = {
            totalLogs: this.logs.length,
            logsByType: {},
            logsByUser: {},
            logsByPage: {},
            recentActivity: this.logs.slice(-10)
        };

        // إحصائيات حسب النوع
        this.logs.forEach(log => {
            stats.logsByType[log.type] = (stats.logsByType[log.type] || 0) + 1;
            stats.logsByUser[log.userName] = (stats.logsByUser[log.userName] || 0) + 1;
            stats.logsByPage[log.page] = (stats.logsByPage[log.page] || 0) + 1;
        });

        return stats;
    }
}

// إنشاء مثيل النظام
window.autoLoggingSystem = new AutoLoggingSystem();

// دوال مساعدة للاستخدام السهل
window.logAction = (type, action, details) => {
    window.autoLoggingSystem.log(type, action, details);
};

window.logCreate = (action, details) => {
    window.autoLoggingSystem.log(window.autoLoggingSystem.logTypes.CREATE, action, details);
};

window.logUpdate = (action, details) => {
    window.autoLoggingSystem.log(window.autoLoggingSystem.logTypes.UPDATE, action, details);
};

window.logDelete = (action, details) => {
    window.autoLoggingSystem.log(window.autoLoggingSystem.logTypes.DELETE, action, details);
};

window.logPayment = (action, details) => {
    window.autoLoggingSystem.log(window.autoLoggingSystem.logTypes.PAYMENT, action, details);
};

// تصدير النظام
export { AutoLoggingSystem };

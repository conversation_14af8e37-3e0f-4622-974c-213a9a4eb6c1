// ===================================================================
//                    نظام طباعة الوصولات المحسن
// ===================================================================

class ReceiptPrintingSystem {
    constructor() {
        this.templates = {
            payment: 'payment-receipt',
            expense: 'expense-receipt',
            registration: 'registration-receipt',
            transport: 'transport-receipt'
        };
        this.settings = {
            schoolName: 'مؤسسة النور التربوي للتعليم الخصوصي',
            schoolAddress: 'العنوان: [يرجى تحديد العنوان في الإعدادات]',
            schoolPhone: 'الهاتف: [يرجى تحديد رقم الهاتف في الإعدادات]',
            schoolEmail: 'البريد الإلكتروني: [يرجى تحديد البريد الإلكتروني في الإعدادات]',
            logoUrl: 'logo.png',
            receiptFooter: 'شكراً لكم على ثقتكم - مؤسسة النور التربوي',
            currency: 'درهم مغربي'
        };
        this.init();
    }

    // تهيئة النظام
    init() {
        this.loadSettings();
        this.createPrintStyles();
        this.setupPrintEventHandlers();
    }

    // تحميل الإعدادات
    loadSettings() {
        try {
            const savedSettings = localStorage.getItem('sbea_receipt_settings');
            if (savedSettings) {
                this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
            }
        } catch (error) {
            console.warn('خطأ في تحميل إعدادات الطباعة:', error);
        }
    }

    // حفظ الإعدادات
    saveSettings() {
        try {
            localStorage.setItem('sbea_receipt_settings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('خطأ في حفظ إعدادات الطباعة:', error);
        }
    }

    // إنشاء أنماط الطباعة
    createPrintStyles() {
        const printStyles = document.createElement('style');
        printStyles.id = 'receipt-print-styles';
        printStyles.textContent = `
            @media print {
                body * {
                    visibility: hidden;
                }
                
                .receipt-container,
                .receipt-container * {
                    visibility: visible;
                }
                
                .receipt-container {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    background: white;
                    color: black;
                    font-family: 'Tajawal', Arial, sans-serif;
                    direction: rtl;
                }
                
                .receipt-header {
                    text-align: center;
                    border-bottom: 2px solid #000;
                    padding-bottom: 20px;
                    margin-bottom: 20px;
                }
                
                .receipt-logo {
                    max-width: 80px;
                    max-height: 80px;
                    margin-bottom: 10px;
                }
                
                .receipt-school-name {
                    font-size: 24px;
                    font-weight: bold;
                    margin: 10px 0;
                    color: #000;
                }
                
                .receipt-school-info {
                    font-size: 12px;
                    color: #666;
                    line-height: 1.4;
                }
                
                .receipt-title {
                    font-size: 20px;
                    font-weight: bold;
                    text-align: center;
                    margin: 20px 0;
                    padding: 10px;
                    background: #f0f0f0;
                    border: 1px solid #000;
                }
                
                .receipt-info {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin-bottom: 20px;
                }
                
                .receipt-info-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 5px 0;
                    border-bottom: 1px dotted #ccc;
                }
                
                .receipt-info-label {
                    font-weight: bold;
                }
                
                .receipt-details {
                    margin: 20px 0;
                }
                
                .receipt-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                }
                
                .receipt-table th,
                .receipt-table td {
                    border: 1px solid #000;
                    padding: 8px;
                    text-align: center;
                }
                
                .receipt-table th {
                    background: #f0f0f0;
                    font-weight: bold;
                }
                
                .receipt-total {
                    text-align: center;
                    font-size: 18px;
                    font-weight: bold;
                    margin: 20px 0;
                    padding: 15px;
                    border: 2px solid #000;
                    background: #f9f9f9;
                }
                
                .receipt-footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #000;
                    font-size: 12px;
                    color: #666;
                }
                
                .receipt-signature {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 50px;
                    margin-top: 40px;
                }
                
                .signature-box {
                    text-align: center;
                    border-top: 1px solid #000;
                    padding-top: 10px;
                }
                
                .receipt-barcode {
                    text-align: center;
                    margin: 20px 0;
                }
                
                .no-print {
                    display: none !important;
                }
            }
            
            .receipt-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                background: white;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                border-radius: 10px;
            }
            
            .receipt-actions {
                text-align: center;
                margin: 20px 0;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 5px;
            }
            
            .receipt-actions button {
                margin: 0 10px;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-weight: bold;
                transition: all 0.3s ease;
            }
            
            .print-btn {
                background: #007bff;
                color: white;
            }
            
            .print-btn:hover {
                background: #0056b3;
            }
            
            .download-btn {
                background: #28a745;
                color: white;
            }
            
            .download-btn:hover {
                background: #1e7e34;
            }
            
            .close-btn {
                background: #6c757d;
                color: white;
            }
            
            .close-btn:hover {
                background: #545b62;
            }
        `;
        
        document.head.appendChild(printStyles);
    }

    // إعداد معالجات أحداث الطباعة
    setupPrintEventHandlers() {
        // معالج الطباعة العام
        document.addEventListener('click', (event) => {
            if (event.target.classList.contains('print-receipt-btn')) {
                const receiptData = JSON.parse(event.target.dataset.receipt || '{}');
                this.printReceipt(receiptData);
            }
        });
    }

    // طباعة وصل دفع
    printPaymentReceipt(paymentData) {
        const receiptData = {
            type: 'payment',
            title: 'وصل استلام',
            studentName: paymentData.studentName,
            studentBarcode: paymentData.studentBarcode,
            amount: paymentData.amount,
            month: paymentData.month,
            paymentDate: paymentData.paymentDate || new Date().toISOString(),
            receiptNumber: this.generateReceiptNumber(),
            paymentMethod: paymentData.paymentMethod || 'نقداً',
            notes: paymentData.notes || '',
            additionalInfo: paymentData.additionalInfo || {}
        };

        this.showReceiptPreview(receiptData);
    }

    // طباعة وصل مصروف
    printExpenseReceipt(expenseData) {
        const receiptData = {
            type: 'expense',
            title: 'وصل صرف',
            description: expenseData.description,
            amount: expenseData.amount,
            category: expenseData.category,
            paymentMethod: expenseData.paymentMethod,
            expenseDate: expenseData.date,
            receiptNumber: this.generateReceiptNumber(),
            notes: expenseData.notes || '',
            recipient: expenseData.recipient || 'غير محدد'
        };

        this.showReceiptPreview(receiptData);
    }

    // طباعة وصل تسجيل
    printRegistrationReceipt(registrationData) {
        const receiptData = {
            type: 'registration',
            title: 'وصل رسوم التسجيل',
            studentName: registrationData.studentName,
            studentBarcode: registrationData.studentBarcode,
            level: registrationData.level,
            group: registrationData.group,
            registrationFee: registrationData.registrationFee,
            booksFee: registrationData.booksFee,
            totalAmount: registrationData.registrationFee + registrationData.booksFee,
            paymentDate: new Date().toISOString(),
            receiptNumber: this.generateReceiptNumber(),
            academicYear: this.getCurrentAcademicYear()
        };

        this.showReceiptPreview(receiptData);
    }

    // عرض معاينة الوصل
    showReceiptPreview(receiptData) {
        const receiptHtml = this.generateReceiptHtml(receiptData);
        
        // إنشاء نافذة منبثقة للمعاينة
        const modal = document.createElement('div');
        modal.className = 'modal receipt-preview-modal';
        modal.style.display = 'block';
        modal.innerHTML = `
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3>معاينة الوصل</h3>
                    <span class="close-modal">&times;</span>
                </div>
                <div class="modal-body">
                    ${receiptHtml}
                    <div class="receipt-actions no-print">
                        <button class="print-btn" onclick="window.print()">
                            <i class="fas fa-print"></i> طباعة
                        </button>
                        <button class="download-btn" onclick="receiptPrintingSystem.downloadReceiptAsPDF('${receiptData.receiptNumber}')">
                            <i class="fas fa-download"></i> تحميل PDF
                        </button>
                        <button class="close-btn" onclick="this.closest('.modal').remove()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // إعداد إغلاق النافذة
        modal.querySelector('.close-modal').addEventListener('click', () => {
            modal.remove();
        });

        // تسجيل عملية الطباعة
        if (window.autoLoggingSystem) {
            window.autoLoggingSystem.log('طباعة', `طباعة وصل: ${receiptData.title}`, {
                receiptNumber: receiptData.receiptNumber,
                type: receiptData.type,
                amount: receiptData.amount
            });
        }
    }

    // توليد HTML للوصل
    generateReceiptHtml(data) {
        const currentDate = new Date().toLocaleDateString('ar-MA');
        const currentTime = new Date().toLocaleTimeString('ar-MA');

        let html = `
            <div class="receipt-container">
                <div class="receipt-header">
                    <img src="${this.settings.logoUrl}" alt="شعار المؤسسة" class="receipt-logo">
                    <div class="receipt-school-name">${this.settings.schoolName}</div>
                    <div class="receipt-school-info">
                        ${this.settings.schoolAddress}<br>
                        ${this.settings.schoolPhone}<br>
                        ${this.settings.schoolEmail}
                    </div>
                </div>

                <div class="receipt-title">${data.title}</div>

                <div class="receipt-info">
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">رقم الوصل:</span>
                        <span>${data.receiptNumber}</span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">التاريخ:</span>
                        <span>${currentDate}</span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">الوقت:</span>
                        <span>${currentTime}</span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">المستخدم:</span>
                        <span>${window.authSystem ? window.authSystem.getCurrentUser()?.username : 'غير محدد'}</span>
                    </div>
                </div>
        `;

        // إضافة تفاصيل حسب نوع الوصل
        if (data.type === 'payment') {
            html += this.generatePaymentDetails(data);
        } else if (data.type === 'expense') {
            html += this.generateExpenseDetails(data);
        } else if (data.type === 'registration') {
            html += this.generateRegistrationDetails(data);
        }

        // إضافة المجموع والتوقيعات
        html += `
                <div class="receipt-total">
                    المبلغ الإجمالي: ${data.amount || data.totalAmount} ${this.settings.currency}
                </div>

                <div class="receipt-signature">
                    <div class="signature-box">
                        <div>توقيع المستلم</div>
                    </div>
                    <div class="signature-box">
                        <div>توقيع المسؤول</div>
                    </div>
                </div>

                <div class="receipt-footer">
                    ${this.settings.receiptFooter}<br>
                    تم الإنشاء تلقائياً بواسطة نظام إدارة مؤسسة النور التربوي
                </div>
            </div>
        `;

        return html;
    }

    // توليد تفاصيل وصل الدفع
    generatePaymentDetails(data) {
        return `
            <div class="receipt-details">
                <table class="receipt-table">
                    <tr>
                        <th>اسم التلميذ</th>
                        <td>${data.studentName}</td>
                    </tr>
                    <tr>
                        <th>الرقم التسلسلي</th>
                        <td>${data.studentBarcode}</td>
                    </tr>
                    <tr>
                        <th>الشهر</th>
                        <td>${data.month}</td>
                    </tr>
                    <tr>
                        <th>طريقة الدفع</th>
                        <td>${data.paymentMethod}</td>
                    </tr>
                    ${data.notes ? `<tr><th>ملاحظات</th><td>${data.notes}</td></tr>` : ''}
                </table>
            </div>
        `;
    }

    // توليد تفاصيل وصل المصروف
    generateExpenseDetails(data) {
        return `
            <div class="receipt-details">
                <table class="receipt-table">
                    <tr>
                        <th>وصف المصروف</th>
                        <td>${data.description}</td>
                    </tr>
                    <tr>
                        <th>الفئة</th>
                        <td>${data.category}</td>
                    </tr>
                    <tr>
                        <th>طريقة الدفع</th>
                        <td>${data.paymentMethod}</td>
                    </tr>
                    <tr>
                        <th>المستفيد</th>
                        <td>${data.recipient}</td>
                    </tr>
                    ${data.notes ? `<tr><th>ملاحظات</th><td>${data.notes}</td></tr>` : ''}
                </table>
            </div>
        `;
    }

    // توليد تفاصيل وصل التسجيل
    generateRegistrationDetails(data) {
        return `
            <div class="receipt-details">
                <table class="receipt-table">
                    <tr>
                        <th>اسم التلميذ</th>
                        <td>${data.studentName}</td>
                    </tr>
                    <tr>
                        <th>الرقم التسلسلي</th>
                        <td>${data.studentBarcode}</td>
                    </tr>
                    <tr>
                        <th>المستوى</th>
                        <td>${data.level}</td>
                    </tr>
                    <tr>
                        <th>الفوج</th>
                        <td>${data.group}</td>
                    </tr>
                    <tr>
                        <th>السنة الدراسية</th>
                        <td>${data.academicYear}</td>
                    </tr>
                </table>
                
                <table class="receipt-table">
                    <thead>
                        <tr>
                            <th>البيان</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>رسوم التسجيل</td>
                            <td>${data.registrationFee} ${this.settings.currency}</td>
                        </tr>
                        <tr>
                            <td>رسوم الكتب</td>
                            <td>${data.booksFee} ${this.settings.currency}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }

    // توليد رقم وصل
    generateReceiptNumber() {
        const date = new Date();
        const year = date.getFullYear().toString().substr(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const time = date.getTime().toString().substr(-4);
        
        return `${year}${month}${day}${time}`;
    }

    // الحصول على السنة الدراسية الحالية
    getCurrentAcademicYear() {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        
        if (currentMonth >= 9) {
            return `${currentYear}-${currentYear + 1}`;
        } else {
            return `${currentYear - 1}-${currentYear}`;
        }
    }

    // تحميل الوصل كـ PDF (يتطلب مكتبة إضافية)
    downloadReceiptAsPDF(receiptNumber) {
        // هذه الوظيفة تتطلب مكتبة jsPDF
        if (typeof jsPDF === 'undefined') {
            alert('مكتبة PDF غير متوفرة. يرجى استخدام خيار الطباعة.');
            return;
        }
        
        // سيتم تطوير هذه الوظيفة لاحقاً
        alert('سيتم تفعيل تحميل PDF قريباً');
    }

    // تحديث إعدادات الطباعة
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
    }
}

// إنشاء مثيل النظام
window.receiptPrintingSystem = new ReceiptPrintingSystem();

// دوال مساعدة للاستخدام السهل
window.printPaymentReceipt = (paymentData) => {
    window.receiptPrintingSystem.printPaymentReceipt(paymentData);
};

window.printExpenseReceipt = (expenseData) => {
    window.receiptPrintingSystem.printExpenseReceipt(expenseData);
};

window.printRegistrationReceipt = (registrationData) => {
    window.receiptPrintingSystem.printRegistrationReceipt(registrationData);
};

// تصدير النظام
export { ReceiptPrintingSystem };

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>نظام النور التربوي - يعمل</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial; background: #f0f0f0; direction: rtl; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        h1 { text-align: center; background: #007bff; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-danger { background: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: center; }
        th { background: #007bff; color: white; }
        tr:hover { background: #f5f5f5; }
        input, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat { background: #f8f9fa; padding: 20px; border-radius: 5px; text-align: center; flex: 1; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 10% auto; padding: 20px; border-radius: 10px; max-width: 500px; }
        .close { float: left; font-size: 28px; cursor: pointer; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .alert-success { background: #d4edda; color: #155724; }
        .alert-error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏫 نظام إدارة مؤسسة النور التربوي</h1>
        
        <div id="alerts"></div>
        
        <!-- إحصائيات -->
        <div class="stats">
            <div class="stat">
                <div class="stat-number" id="total-students">0</div>
                <div>التلاميذ</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="total-fees">0</div>
                <div>إجمالي الرسوم</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="total-paid">0</div>
                <div>المدفوع</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="total-remaining">0</div>
                <div>المتبقي</div>
            </div>
        </div>
        
        <!-- أزرار -->
        <div>
            <button class="btn btn-success" onclick="showAddForm()">➕ إضافة تلميذ</button>
            <button class="btn" onclick="refreshData()">🔄 تحديث</button>
            <button class="btn btn-danger" onclick="clearAll()">🗑️ مسح الكل</button>
        </div>
        
        <!-- جدول التلاميذ -->
        <table>
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الاسم</th>
                    <th>المستوى</th>
                    <th>الرسوم الشهرية</th>
                    <th>المدفوع</th>
                    <th>المتبقي</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody id="students-table">
                <tr><td colspan="7">لا توجد بيانات</td></tr>
            </tbody>
        </table>
    </div>

    <!-- نافذة إضافة تلميذ -->
    <div id="add-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>إضافة تلميذ جديد</h3>
            <input type="text" id="name" placeholder="اسم التلميذ" required>
            <select id="level" required>
                <option value="">اختر المستوى</option>
                <option>الأول ابتدائي</option>
                <option>الثاني ابتدائي</option>
                <option>الثالث ابتدائي</option>
                <option>الرابع ابتدائي</option>
                <option>الخامس ابتدائي</option>
                <option>السادس ابتدائي</option>
            </select>
            <input type="number" id="fee" placeholder="الرسوم الشهرية" min="0">
            <input type="tel" id="phone" placeholder="رقم الهاتف">
            <br><br>
            <button class="btn btn-success" onclick="addStudent()">إضافة</button>
            <button class="btn" onclick="closeModal()">إلغاء</button>
        </div>
    </div>

    <!-- نافذة الدفع -->
    <div id="pay-modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closePayModal()">&times;</span>
            <h3>تسجيل دفع</h3>
            <div id="pay-info"></div>
            <input type="number" id="pay-amount" placeholder="مبلغ الدفع" min="0">
            <br><br>
            <button class="btn btn-success" onclick="processPay()">تسجيل الدفع</button>
            <button class="btn" onclick="closePayModal()">إلغاء</button>
        </div>
    </div>

    <script>
        let students = [];
        let nextId = 1;
        let currentPayStudent = null;

        // تحميل البيانات
        function loadData() {
            const saved = localStorage.getItem('nour_students');
            if (saved) {
                students = JSON.parse(saved);
            }
            const savedId = localStorage.getItem('nour_next_id');
            if (savedId) {
                nextId = parseInt(savedId);
            }
        }

        // حفظ البيانات
        function saveData() {
            localStorage.setItem('nour_students', JSON.stringify(students));
            localStorage.setItem('nour_next_id', nextId.toString());
        }

        // عرض رسالة
        function showAlert(msg, type = 'success') {
            const alerts = document.getElementById('alerts');
            alerts.innerHTML = `<div class="alert alert-${type}">${msg}</div>`;
            setTimeout(() => alerts.innerHTML = '', 3000);
        }

        // حساب الإحصائيات
        function updateStats() {
            let totalFees = 0, totalPaid = 0;
            students.forEach(s => {
                const yearlyFee = (s.monthlyFee || 0) * 10;
                totalFees += yearlyFee;
                totalPaid += s.totalPaid || 0;
            });
            
            document.getElementById('total-students').textContent = students.length;
            document.getElementById('total-fees').textContent = totalFees;
            document.getElementById('total-paid').textContent = totalPaid;
            document.getElementById('total-remaining').textContent = totalFees - totalPaid;
        }

        // عرض التلاميذ
        function displayStudents() {
            const tbody = document.getElementById('students-table');
            if (students.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7">لا توجد بيانات</td></tr>';
                return;
            }

            let html = '';
            students.forEach(s => {
                const yearlyFee = (s.monthlyFee || 0) * 10;
                const remaining = yearlyFee - (s.totalPaid || 0);
                html += `
                    <tr>
                        <td>${s.id}</td>
                        <td>${s.name}</td>
                        <td>${s.level}</td>
                        <td>${s.monthlyFee || 0}</td>
                        <td style="color: green">${s.totalPaid || 0}</td>
                        <td style="color: ${remaining > 0 ? 'red' : 'green'}">${remaining}</td>
                        <td>
                            <button class="btn" onclick="openPay(${s.id})">💰</button>
                            <button class="btn btn-danger" onclick="deleteStudent(${s.id})">🗑️</button>
                        </td>
                    </tr>
                `;
            });
            tbody.innerHTML = html;
        }

        // إضافة تلميذ
        function addStudent() {
            const name = document.getElementById('name').value.trim();
            const level = document.getElementById('level').value;
            const fee = parseFloat(document.getElementById('fee').value) || 0;
            const phone = document.getElementById('phone').value.trim();

            if (!name || !level) {
                showAlert('يرجى ملء الاسم والمستوى', 'error');
                return;
            }

            students.push({
                id: nextId++,
                name: name,
                level: level,
                monthlyFee: fee,
                phone: phone,
                totalPaid: 0
            });

            saveData();
            displayStudents();
            updateStats();
            closeModal();
            showAlert(`تم إضافة ${name} بنجاح`);
            
            // مسح النموذج
            document.getElementById('name').value = '';
            document.getElementById('level').value = '';
            document.getElementById('fee').value = '';
            document.getElementById('phone').value = '';
        }

        // حذف تلميذ
        function deleteStudent(id) {
            const student = students.find(s => s.id === id);
            if (confirm(`حذف ${student.name}؟`)) {
                students = students.filter(s => s.id !== id);
                saveData();
                displayStudents();
                updateStats();
                showAlert(`تم حذف ${student.name}`);
            }
        }

        // فتح نافذة الدفع
        function openPay(id) {
            currentPayStudent = students.find(s => s.id === id);
            const yearlyFee = (currentPayStudent.monthlyFee || 0) * 10;
            const remaining = yearlyFee - (currentPayStudent.totalPaid || 0);
            
            document.getElementById('pay-info').innerHTML = `
                <p><strong>${currentPayStudent.name}</strong></p>
                <p>الرسوم السنوية: ${yearlyFee}</p>
                <p>المدفوع: ${currentPayStudent.totalPaid || 0}</p>
                <p>المتبقي: ${remaining}</p>
            `;
            document.getElementById('pay-amount').value = remaining;
            document.getElementById('pay-modal').style.display = 'block';
        }

        // معالجة الدفع
        function processPay() {
            const amount = parseFloat(document.getElementById('pay-amount').value) || 0;
            if (amount <= 0) {
                showAlert('أدخل مبلغ صحيح', 'error');
                return;
            }

            currentPayStudent.totalPaid = (currentPayStudent.totalPaid || 0) + amount;
            saveData();
            displayStudents();
            updateStats();
            closePayModal();
            showAlert(`تم تسجيل دفع ${amount} لـ ${currentPayStudent.name}`);
        }

        // فتح/إغلاق النوافذ
        function showAddForm() {
            document.getElementById('add-modal').style.display = 'block';
        }
        function closeModal() {
            document.getElementById('add-modal').style.display = 'none';
        }
        function closePayModal() {
            document.getElementById('pay-modal').style.display = 'none';
        }

        // تحديث البيانات
        function refreshData() {
            displayStudents();
            updateStats();
            showAlert('تم التحديث');
        }

        // مسح جميع البيانات
        function clearAll() {
            if (confirm('مسح جميع البيانات؟')) {
                students = [];
                nextId = 1;
                localStorage.removeItem('nour_students');
                localStorage.removeItem('nour_next_id');
                displayStudents();
                updateStats();
                showAlert('تم مسح جميع البيانات');
            }
        }

        // إغلاق النوافذ عند النقر خارجها
        window.onclick = function(event) {
            const addModal = document.getElementById('add-modal');
            const payModal = document.getElementById('pay-modal');
            if (event.target === addModal) addModal.style.display = 'none';
            if (event.target === payModal) payModal.style.display = 'none';
        }

        // تشغيل التطبيق
        loadData();
        displayStudents();
        updateStats();
        showAlert('مرحباً - النظام جاهز للعمل');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .diagnostics-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .diagnostic-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }
        
        .diagnostic-card.success {
            border-left-color: #28a745;
        }
        
        .diagnostic-card.warning {
            border-left-color: #ffc107;
        }
        
        .diagnostic-card.error {
            border-left-color: #dc3545;
        }
        
        .diagnostic-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .diagnostic-header i {
            font-size: 20px;
        }
        
        .diagnostic-header.success i {
            color: #28a745;
        }
        
        .diagnostic-header.warning i {
            color: #ffc107;
        }
        
        .diagnostic-header.error i {
            color: #dc3545;
        }
        
        .diagnostic-details {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .diagnostic-list {
            list-style: none;
            padding: 0;
        }
        
        .diagnostic-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .diagnostic-list li:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-badge.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-badge.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-badge.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .run-diagnostics-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }
        
        .run-diagnostics-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading i {
            font-size: 24px;
            color: #007bff;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <header>
        <img src="logo" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="profile.html">الملف الشخصي</a></li>
                <li><a href="diagnostics.html" class="active">تشخيص النظام</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="diagnostics">
            <div class="diagnostics-container">
                <h2><i class="fas fa-stethoscope"></i> تشخيص النظام</h2>
                <p>فحص شامل لحالة النظام والتأكد من سلامة جميع المكونات</p>
                
                <button class="run-diagnostics-btn" onclick="runDiagnostics()">
                    <i class="fas fa-play"></i> تشغيل التشخيص
                </button>
                
                <div class="loading" id="loading">
                    <i class="fas fa-spinner"></i>
                    <p>جاري فحص النظام...</p>
                </div>
                
                <div id="diagnostics-results">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>مؤسسة النور التربوي - نظام إدارة شامل للمؤسسات التعليمية.</p>
    </footer>

    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
    <script>
        async function runDiagnostics() {
            const loadingDiv = document.getElementById('loading');
            const resultsDiv = document.getElementById('diagnostics-results');
            
            loadingDiv.style.display = 'block';
            resultsDiv.innerHTML = '';
            
            // محاكاة وقت الفحص
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const diagnostics = await performSystemDiagnostics();
            
            loadingDiv.style.display = 'none';
            displayDiagnosticsResults(diagnostics);
        }
        
        async function performSystemDiagnostics() {
            const results = [];
            
            // فحص المصادقة
            results.push(checkAuthentication());
            
            // فحص البيانات
            results.push(checkDataIntegrity());
            
            // فحص المكتبات الخارجية
            results.push(checkExternalLibraries());
            
            // فحص التخزين المحلي
            results.push(checkLocalStorage());
            
            // فحص المتصفح
            results.push(checkBrowserCompatibility());
            
            // فحص الأداء
            results.push(checkPerformance());
            
            return results;
        }
        
        function checkAuthentication() {
            const isLoggedIn = window.authSystem && window.authSystem.isLoggedIn();
            const currentUser = window.authSystem && window.authSystem.getCurrentUser();
            
            return {
                title: 'نظام المصادقة',
                status: isLoggedIn ? 'success' : 'error',
                message: isLoggedIn ? 'نظام المصادقة يعمل بشكل صحيح' : 'مشكلة في نظام المصادقة',
                details: [
                    { name: 'حالة تسجيل الدخول', value: isLoggedIn ? 'مسجل دخول' : 'غير مسجل دخول', status: isLoggedIn ? 'success' : 'error' },
                    { name: 'المستخدم الحالي', value: currentUser ? currentUser.name : 'غير محدد', status: currentUser ? 'success' : 'warning' },
                    { name: 'الصلاحيات', value: currentUser ? currentUser.permissions.length + ' صلاحية' : '0', status: currentUser ? 'success' : 'warning' }
                ]
            };
        }
        
        function checkDataIntegrity() {
            try {
                const students = JSON.parse(localStorage.getItem('sbea_students') || '[]');
                const teachers = JSON.parse(localStorage.getItem('sbea_teachers') || '[]');
                const users = JSON.parse(localStorage.getItem('sbea_users') || '[]');
                
                const hasData = students.length > 0 || teachers.length > 0 || users.length > 0;
                
                return {
                    title: 'سلامة البيانات',
                    status: 'success',
                    message: 'البيانات سليمة ومقروءة',
                    details: [
                        { name: 'التلاميذ', value: students.length + ' تلميذ', status: 'success' },
                        { name: 'الأساتذة', value: teachers.length + ' أستاذ', status: 'success' },
                        { name: 'المستخدمون', value: users.length + ' مستخدم', status: 'success' }
                    ]
                };
            } catch (error) {
                return {
                    title: 'سلامة البيانات',
                    status: 'error',
                    message: 'خطأ في قراءة البيانات: ' + error.message,
                    details: []
                };
            }
        }
        
        function checkExternalLibraries() {
            const libraries = {
                'JsBarcode': typeof JsBarcode !== 'undefined',
                'XLSX': typeof XLSX !== 'undefined',
                'Font Awesome': document.querySelector('link[href*="font-awesome"]') !== null
            };
            
            const allLoaded = Object.values(libraries).every(loaded => loaded);
            
            return {
                title: 'المكتبات الخارجية',
                status: allLoaded ? 'success' : 'warning',
                message: allLoaded ? 'جميع المكتبات محملة' : 'بعض المكتبات غير محملة',
                details: Object.entries(libraries).map(([name, loaded]) => ({
                    name,
                    value: loaded ? 'محملة' : 'غير محملة',
                    status: loaded ? 'success' : 'warning'
                }))
            };
        }
        
        function checkLocalStorage() {
            try {
                const testKey = 'test_storage';
                localStorage.setItem(testKey, 'test');
                localStorage.removeItem(testKey);
                
                let totalSize = 0;
                for (let key in localStorage) {
                    if (key.startsWith('sbea_')) {
                        totalSize += localStorage[key].length;
                    }
                }
                
                const sizeInKB = Math.round(totalSize / 1024);
                const maxSize = 5 * 1024; // 5MB تقريباً
                
                return {
                    title: 'التخزين المحلي',
                    status: sizeInKB < maxSize ? 'success' : 'warning',
                    message: 'التخزين المحلي يعمل بشكل صحيح',
                    details: [
                        { name: 'حجم البيانات', value: sizeInKB + ' KB', status: sizeInKB < maxSize ? 'success' : 'warning' },
                        { name: 'المساحة المتاحة', value: 'متاحة', status: 'success' }
                    ]
                };
            } catch (error) {
                return {
                    title: 'التخزين المحلي',
                    status: 'error',
                    message: 'مشكلة في التخزين المحلي: ' + error.message,
                    details: []
                };
            }
        }
        
        function checkBrowserCompatibility() {
            const features = {
                'LocalStorage': typeof Storage !== 'undefined',
                'BroadcastChannel': typeof BroadcastChannel !== 'undefined',
                'FileReader': typeof FileReader !== 'undefined',
                'Blob': typeof Blob !== 'undefined'
            };
            
            const allSupported = Object.values(features).every(supported => supported);
            
            return {
                title: 'توافق المتصفح',
                status: allSupported ? 'success' : 'warning',
                message: allSupported ? 'المتصفح يدعم جميع الميزات' : 'بعض الميزات غير مدعومة',
                details: Object.entries(features).map(([name, supported]) => ({
                    name,
                    value: supported ? 'مدعوم' : 'غير مدعوم',
                    status: supported ? 'success' : 'warning'
                }))
            };
        }
        
        function checkPerformance() {
            const startTime = performance.now();
            
            // محاكاة عملية
            for (let i = 0; i < 10000; i++) {
                Math.random();
            }
            
            const endTime = performance.now();
            const executionTime = endTime - startTime;
            
            return {
                title: 'الأداء',
                status: executionTime < 100 ? 'success' : 'warning',
                message: 'فحص أداء النظام',
                details: [
                    { name: 'وقت التنفيذ', value: executionTime.toFixed(2) + ' ms', status: executionTime < 100 ? 'success' : 'warning' },
                    { name: 'استخدام الذاكرة', value: 'طبيعي', status: 'success' }
                ]
            };
        }
        
        function displayDiagnosticsResults(diagnostics) {
            const resultsDiv = document.getElementById('diagnostics-results');
            
            let html = '';
            diagnostics.forEach(diagnostic => {
                html += `
                    <div class="diagnostic-card ${diagnostic.status}">
                        <div class="diagnostic-header ${diagnostic.status}">
                            <i class="fas fa-${diagnostic.status === 'success' ? 'check-circle' : diagnostic.status === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
                            <h3>${diagnostic.title}</h3>
                        </div>
                        <p>${diagnostic.message}</p>
                        ${diagnostic.details.length > 0 ? `
                            <div class="diagnostic-details">
                                <ul class="diagnostic-list">
                                    ${diagnostic.details.map(detail => `
                                        <li>
                                            <span>${detail.name}</span>
                                            <span class="status-badge ${detail.status}">${detail.value}</span>
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        // تشغيل التشخيص تلقائياً عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runDiagnostics, 1000);
        });
    </script>
</body>
</html>

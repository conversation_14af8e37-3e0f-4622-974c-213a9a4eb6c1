# 🏫 نظام إدارة مؤسسة النور التربوي

نظام إدارة شامل ومتطور للمؤسسات التعليمية مع دعم متعدد المستخدمين والمزامنة الفورية.

## 🌟 المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- **تسجيل دخول آمن** مع ثلاثة أنواع مستخدمين (مدير، أستاذ، موظف)
- **صلاحيات مفصلة** لكل نوع مستخدم
- **جلسات آمنة** مع انتهاء صلاحية تلقائي
- **سجل شامل للأنشطة** مع تتبع جميع العمليات

### 👥 إدارة المستخدمين
- **إضافة وتعديل المستخدمين** مع تخصيص الصلاحيات
- **إدارة كلمات المرور** وإعادة تعيينها
- **ملفات شخصية** قابلة للتخصيص
- **إحصائيات المستخدمين** والأنشطة

### 🎓 إدارة التلاميذ
- **قاعدة بيانات شاملة** للتلاميذ مع الصور
- **رموز شريطية** لكل تلميذ
- **تتبع الدفعات** والمستحقات
- **إدارة النقل** والرسوم الإضافية
- **تصدير واستيراد** البيانات

### 👨‍🏫 إدارة الأساتذة والموظفين
- **ملفات شاملة** للأساتذة والموظفين
- **تخصيص المواد** والمستويات للأساتذة
- **إدارة الرواتب** والمهام
- **تتبع الحضور** والأداء

### 📚 إدارة المجموعات والمناهج
- **تنظيم المستويات** والأفواج
- **إدارة المناهج** والمواد الدراسية
- **زيارات المفتشين** والتقييمات
- **المسابقات التعليمية**

### 💰 النظام المالي
- **تتبع الدفعات** الشهرية والجزئية
- **وصولات دفع** قابلة للطباعة
- **تقارير مالية** مفصلة
- **إحصائيات الإيرادات** والمصروفات

### 📱 تكامل WhatsApp
- **إرسال رسائل** فردية وجماعية
- **إنشاء مجموعات** تلقائياً
- **قوالب رسائل** جاهزة
- **تتبع حالة الرسائل**

### 🔄 المزامنة الفورية
- **تحديث فوري** للبيانات بين المستخدمين
- **إشعارات في الوقت الفعلي**
- **حل تضارب البيانات** تلقائياً
- **عمل دون اتصال** مع مزامنة لاحقة

## 🚀 كيفية الاستخدام

### 1. تسجيل الدخول
```
المدير:
اسم المستخدم: admin
كلمة المرور: admin123

الأستاذ:
اسم المستخدم: teacher
كلمة المرور: teacher123

الموظف:
اسم المستخدم: staff
كلمة المرور: staff123
```

### 2. الصلاحيات حسب النوع

#### 👨‍💼 المدير
- جميع الصلاحيات
- إدارة المستخدمين
- الإعدادات العامة
- التقارير الشاملة

#### 👨‍🏫 الأستاذ
- عرض وتعديل بيانات التلاميذ
- إدارة المجموعات والمواد
- عرض الدفعات
- إرسال رسائل WhatsApp

#### 👨‍💼 الموظف
- عرض بيانات التلاميذ
- تسجيل الدفعات
- العمليات المالية
- التقارير المالية

## 📁 هيكل المشروع

```
tex/
├── index.html              # الصفحة الرئيسية
├── login.html              # صفحة تسجيل الدخول
├── students.html           # إدارة التلاميذ
├── teachers.html           # إدارة الأساتذة
├── staff.html              # إدارة الموظفين
├── groups.html             # إدارة المجموعات
├── financial.html          # العمليات المالية
├── payments.html           # عمليات الدفع
├── whatsapp.html           # إدارة WhatsApp
├── users.html              # إدارة المستخدمين
├── profile.html            # الملف الشخصي
├── import-export.html      # الاستيراد والتصدير
├── activities.html         # إدارة الأنشطة
├── settings.html           # الإعدادات
├── script.js               # الوظائف الرئيسية
├── auth.js                 # نظام المصادقة
├── sync.js                 # نظام المزامنة
├── users.js                # إدارة المستخدمين
├── profile.js              # الملف الشخصي
├── style.css               # التصميم
└── logo                    # شعار المؤسسة
```

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6+** - الوظائف والتفاعل
- **LocalStorage** - تخزين البيانات محلياً
- **BroadcastChannel API** - المزامنة بين النوافذ
- **Font Awesome** - الأيقونات
- **JsBarcode** - الرموز الشريطية
- **XLSX** - استيراد وتصدير Excel

## 🔧 التثبيت والإعداد

1. **تحميل الملفات**
   ```bash
   # نسخ جميع الملفات إلى مجلد المشروع
   ```

2. **فتح المشروع**
   ```bash
   # فتح login.html في المتصفح
   file:///path/to/project/login.html
   ```

3. **تسجيل الدخول**
   - استخدم أحد الحسابات الافتراضية
   - أو أنشئ حساب جديد من خلال المدير

## 📊 قاعدة البيانات

النظام يستخدم LocalStorage لحفظ البيانات:

```javascript
// مفاتيح التخزين
sbea_students          // بيانات التلاميذ
sbea_teachers          // بيانات الأساتذة
sbea_staff             // بيانات الموظفين
sbea_groups            // المجموعات والمستويات
sbea_users             // المستخدمين
sbea_payments          // عمليات الدفع
sbea_user_activities   // سجل الأنشطة
sbea_current_user      // المستخدم الحالي
sbea_user_settings     // إعدادات المستخدم
```

## 🔒 الأمان

- **تشفير كلمات المرور** (يمكن تحسينه)
- **التحقق من الصلاحيات** في كل عملية
- **جلسات آمنة** مع انتهاء صلاحية
- **تسجيل جميع الأنشطة**
- **حماية من الوصول غير المصرح**

## 🌐 المتصفحات المدعومة

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 📱 التصميم المتجاوب

النظام مصمم ليعمل على:
- 💻 أجهزة الكمبيوتر
- 📱 الهواتف الذكية
- 📱 الأجهزة اللوحية

## 🔧 التحديثات الأخيرة (v1.1.0)

### ✅ **المشاكل المحلولة:**
- إصلاح مشاكل تسجيل الدخول والمصادقة
- حل مشكلة عدم ظهور الإحصائيات في الصفحة الرئيسية
- إصلاح مشاكل الرموز الشريطية (JsBarcode)
- تحسين معالجة الأخطاء والبيانات التالفة
- إصلاح مشاكل المزامنة بين الصفحات

### 🆕 **الميزات الجديدة:**
- **صفحة تشخيص النظام** - فحص شامل لحالة النظام
- **صفحة الإعدادات المحسنة** - إدارة كاملة للنظام
- **نظام النسخ الاحتياطي المتقدم** - حفظ واستعادة البيانات
- **الوضع المظلم** - راحة أكبر للعينين
- **تحسينات الأداء** - سرعة أكبر في التحميل
- **فحص المكتبات الخارجية** - التأكد من تحميل جميع المكتبات

### 🔒 **تحسينات الأمان:**
- تشفير محسن لكلمات المرور
- جلسات أكثر أماناً
- تسجيل شامل للأنشطة
- حماية متقدمة للصفحات

### 📊 **تحسينات الواجهة:**
- إحصائيات مالية دقيقة
- تصميم متجاوب محسن
- أيقونات وألوان محدثة
- تنقل أسهل بين الصفحات

## 🚀 المميزات المستقبلية

- [ ] قاعدة بيانات خارجية (MySQL/PostgreSQL)
- [ ] تطبيق موبايل
- [ ] تقارير PDF متقدمة
- [x] نظام النسخ الاحتياطي التلقائي ✅
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] إشعارات البريد الإلكتروني
- [ ] نظام الحضور والغياب
- [ ] تقييم الطلاب والدرجات
- [x] صفحة تشخيص النظام ✅
- [x] الوضع المظلم ✅

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشكلة:
1. تحقق من وحدة تحكم المتصفح (F12)
2. تأكد من تفعيل JavaScript
3. امسح ذاكرة التخزين المؤقت
4. أعد تحميل الصفحة

## 📞 الدعم الفني

للحصول على الدعم الفني:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: **********
- 💬 WhatsApp: متاح من خلال النظام

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في تطوير هذا النظام:
- فريق التطوير
- المختبرين
- المستخدمين الأوائل

---

**مؤسسة النور التربوي للتعليم الخصوصي**  
*نظام إدارة شامل للمؤسسات التعليمية*

© 2024 جميع الحقوق محفوظة

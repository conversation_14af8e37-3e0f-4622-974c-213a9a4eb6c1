<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأساتذة - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html" class="active">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="expenses.html">إدارة النفقات</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="teacher-management">
            <h2>إدارة الأساتذة</h2>
            <form id="add-teacher-form" class="detailed-form">
                <div class="form-row">
                    <input type="text" id="teacher-name" placeholder="اسم الأستاذ" required>
                    <input type="number" id="teacher-salary" placeholder="الراتب الشهري (DHS)" required>
                </div>
                <div class="form-row">
                     <textarea id="teacher-tasks" placeholder="مهام إضافية"></textarea>
                </div>

                <div class="form-section">
                    <h3>📚 المستويات الدراسية</h3>
                    <div id="teacher-levels-checklist" class="checklist-group">
                        <!-- Checkboxes for levels will be populated by JS -->
                    </div>
                </div>

                <div class="form-section">
                    <h3>👥 الأفواج</h3>
                    <div id="teacher-groups-checklist" class="checklist-group">
                        <!-- Checkboxes for groups will be populated by JS -->
                    </div>
                </div>

                <div class="form-section">
                    <h3>📖 المواد الدراسية</h3>
                    <div id="teacher-subjects-checklist" class="checklist-group">
                        <!-- Checkboxes for subjects will be populated by JS -->
                    </div>
                </div>
                
                <button type="submit">إضافة أستاذ</button>
            </form>

            <table id="teacher-table">
                <thead>
                    <tr>
                        <th>الرقم الترتيبي</th>
                        <th>اسم الأستاذ</th>
                        <th>الراتب الشهري</th>
                        <th>المستويات</th>
                        <th>الأفواج</th>
                        <th>المواد</th>
                        <th>مهام إضافية</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Teacher rows will be inserted here -->
                </tbody>
            </table>
        </section>
    </main>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <script src="validation.js"></script>
    <script src="notifications.js"></script>
    <script src="loading.js"></script>
    <script src="animations.js"></script>
    <script src="auth.js"></script>
    <script src="sync.js"></script>
    <script src="script.js"></script>
</body>
</html>
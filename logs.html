<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجلات النظام - مؤسسة النور التربوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <img src="logo.png" alt="شعار مؤسسة النور التربوي" class="logo">
        <h1>مؤسسة النور التربوي للتعليم الخصوصي</h1>
        <nav>
            <ul>
                <li><a href="index.html">لوحة التحكم</a></li>
                <li><a href="students.html">إدارة التلاميذ</a></li>
                <li><a href="teachers.html">إدارة الأساتذة</a></li>
                <li><a href="staff.html">إدارة الموظفين</a></li>
                <li><a href="groups.html">إدارة المستويات والأفواج</a></li>
                <li><a href="financial.html">الإجراءات المالية</a></li>
                <li><a href="payments.html">عمليات الدفع</a></li>
                <li><a href="expenses.html">إدارة النفقات</a></li>
                <li><a href="whatsapp.html">إدارة WhatsApp</a></li>
                <li><a href="import-export.html">الاستيراد والتصدير</a></li>
                <li><a href="users.html" data-permission="users_read">إدارة المستخدمين</a></li>
                <li><a href="activities.html">إدارة الأنشطة</a></li>
                <li><a href="logs.html" class="active">سجلات النظام</a></li>
                <li><a href="profile.html">الملف الشخصي</a></li>
                <li><a href="diagnostics.html" data-permission="all">تشخيص النظام</a></li>
                <li><a href="settings.html">الإعدادات</a></li>
            </ul>
        </nav>
        <div class="user-info">
            <span class="current-user-name">المستخدم</span>
            <span class="current-user-role">الدور</span>
            <button onclick="window.authSystem.logout()" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
            </button>
        </div>
    </header>

    <main>
        <section id="logs-management">
            <h2>📋 سجلات النظام التلقائية</h2>
            
            <!-- إحصائيات سريعة -->
            <div class="logs-summary">
                <div class="summary-card total">
                    <div class="summary-icon">📊</div>
                    <div class="summary-content">
                        <h4>إجمالي السجلات</h4>
                        <p id="total-logs-count">0</p>
                    </div>
                </div>
                <div class="summary-card today">
                    <div class="summary-icon">📅</div>
                    <div class="summary-content">
                        <h4>سجلات اليوم</h4>
                        <p id="today-logs-count">0</p>
                    </div>
                </div>
                <div class="summary-card users">
                    <div class="summary-icon">👥</div>
                    <div class="summary-content">
                        <h4>المستخدمون النشطون</h4>
                        <p id="active-users-count">0</p>
                    </div>
                </div>
                <div class="summary-card errors">
                    <div class="summary-icon">⚠️</div>
                    <div class="summary-content">
                        <h4>الأخطاء اليوم</h4>
                        <p id="errors-today-count">0</p>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="logs-filters">
                <div class="filter-group">
                    <label for="filter-log-type">نوع السجل</label>
                    <select id="filter-log-type">
                        <option value="">جميع الأنواع</option>
                        <option value="إنشاء">إنشاء</option>
                        <option value="تحديث">تحديث</option>
                        <option value="حذف">حذف</option>
                        <option value="تسجيل دخول">تسجيل دخول</option>
                        <option value="تسجيل خروج">تسجيل خروج</option>
                        <option value="دفعة">دفعة</option>
                        <option value="تصدير">تصدير</option>
                        <option value="استيراد">استيراد</option>
                        <option value="طباعة">طباعة</option>
                        <option value="عرض">عرض</option>
                        <option value="بحث">بحث</option>
                        <option value="خطأ">خطأ</option>
                        <option value="تحذير">تحذير</option>
                        <option value="معلومات">معلومات</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-user">المستخدم</label>
                    <select id="filter-user">
                        <option value="">جميع المستخدمين</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-page">الصفحة</label>
                    <select id="filter-page">
                        <option value="">جميع الصفحات</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-date-from">من تاريخ</label>
                    <input type="datetime-local" id="filter-date-from">
                </div>
                
                <div class="filter-group">
                    <label for="filter-date-to">إلى تاريخ</label>
                    <input type="datetime-local" id="filter-date-to">
                </div>
                
                <div class="filter-group">
                    <label for="filter-search">البحث في النص</label>
                    <input type="text" id="filter-search" placeholder="ابحث في الإجراءات والتفاصيل...">
                </div>
                
                <div class="filter-actions">
                    <button id="apply-log-filters-btn" class="filter-btn">
                        <i class="fas fa-filter"></i> تطبيق الفلاتر
                    </button>
                    <button id="clear-log-filters-btn" class="filter-btn secondary">
                        <i class="fas fa-times"></i> مسح الفلاتر
                    </button>
                    <button id="refresh-logs-btn" class="filter-btn info">
                        <i class="fas fa-sync"></i> تحديث
                    </button>
                </div>
            </div>

            <!-- إجراءات السجلات -->
            <div class="logs-actions">
                <button id="export-logs-json-btn" class="action-btn">
                    <i class="fas fa-download"></i> تصدير JSON
                </button>
                <button id="export-logs-csv-btn" class="action-btn">
                    <i class="fas fa-file-csv"></i> تصدير CSV
                </button>
                <button id="clear-logs-btn" class="action-btn danger">
                    <i class="fas fa-trash"></i> مسح جميع السجلات
                </button>
                <button id="toggle-logging-btn" class="action-btn warning">
                    <i class="fas fa-pause"></i> إيقاف التسجيل
                </button>
            </div>

            <!-- قائمة السجلات -->
            <div class="logs-container">
                <div id="logs-list" class="logs-table-container">
                    <!-- سيتم ملء السجلات هنا بواسطة JavaScript -->
                </div>
                
                <!-- التصفح -->
                <div class="logs-pagination">
                    <button id="prev-page-btn" class="pagination-btn" disabled>
                        <i class="fas fa-chevron-right"></i> السابق
                    </button>
                    <span id="page-info">الصفحة 1 من 1</span>
                    <button id="next-page-btn" class="pagination-btn" disabled>
                        التالي <i class="fas fa-chevron-left"></i>
                    </button>
                    <select id="page-size-select">
                        <option value="25">25 سجل</option>
                        <option value="50" selected>50 سجل</option>
                        <option value="100">100 سجل</option>
                        <option value="200">200 سجل</option>
                    </select>
                </div>
            </div>

            <!-- الرسم البياني للأنشطة -->
            <div class="logs-charts">
                <h3>📈 إحصائيات الأنشطة</h3>
                <div class="charts-container">
                    <div class="chart-card">
                        <h4>الأنشطة حسب النوع</h4>
                        <canvas id="logs-by-type-chart"></canvas>
                    </div>
                    <div class="chart-card">
                        <h4>الأنشطة حسب الوقت</h4>
                        <canvas id="logs-by-time-chart"></canvas>
                    </div>
                    <div class="chart-card">
                        <h4>المستخدمون الأكثر نشاطاً</h4>
                        <canvas id="logs-by-user-chart"></canvas>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- نافذة تفاصيل السجل -->
    <div id="log-details-modal" class="modal" style="display: none;">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>🔍 تفاصيل السجل</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div id="log-details-content">
                    <!-- سيتم ملء التفاصيل هنا -->
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد مسح السجلات -->
    <div id="clear-logs-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>⚠️ تأكيد مسح السجلات</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من مسح جميع السجلات؟ هذا الإجراء لا يمكن التراجع عنه.</p>
                <div class="modal-actions">
                    <button id="confirm-clear-logs-btn" class="danger-btn">مسح جميع السجلات</button>
                    <button class="cancel-btn">إلغاء</button>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>مؤسسة النور التربوي - الخيار الأمثل لتسيير مؤسسات التعليم الخاصة.</p>
    </footer>

    <!-- تضمين ملفات JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="auth.js"></script>
    <script src="loading.js"></script>
    <script src="notifications.js"></script>
    <script src="validation.js"></script>
    <script src="animations.js"></script>
    <script src="sync.js"></script>
    <script src="auto-logging.js"></script>
    <script src="script.js"></script>
</body>
</html>
